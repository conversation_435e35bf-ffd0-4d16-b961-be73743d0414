<!DOCTYPE html>
<html>

<head>
  <title>From {{ $data['dataFrom']['name'] }} To {{ $data['dataTo']['name'] }}</title>
  <style>
    html {
      margin: 0px;
    }

    @font-face {
      font-family: 'ShipToSmall';
      src: url("{{ secure_asset('fonts/shipping_font/ShipToSmall.ttf') }}") format('truetype');
    }

    @font-face {
      font-family: 'ShipMail';
      src: url("{{ secure_asset('fonts/shipping_font/ShipMail.ttf') }}") format('truetype');
    }

    @font-face {
      font-family: 'ShipState';
      src: url("{{ secure_asset('fonts/shipping_font/ShipState.ttf') }}") format('truetype');
    }

    @font-face {
      font-family: 'ShipSymbol';
      src: url("{{ secure_asset('fonts/shipping_font/ShipSymbol.ttf') }}") format('truetype');
    }

    @font-face {
      font-family: 'ShipToBig';
      src: url("{{ secure_asset('fonts/shipping_font/ShipToBig.ttf') }}") format('truetype');
    }

    @font-face {
      font-family: 'ShipToSmall';
      src: url("{{ secure_asset('fonts/shipping_font/ShipToSmall.ttf') }}") format('truetype');
    }

    @font-face {
      font-family: 'ShipTracking';
      src: url("{{ secure_asset('fonts/shipping_font/ShipTracking.ttf') }}") format('truetype');
    }

    @font-face {
      font-family: 'ShipUPS';
      src: url("{{ secure_asset('fonts/shipping_font/ShipUPS.ttf') }}") format('truetype');
    }

    @font-face {
      font-family: 'ShipReference';
      src: url("{{ secure_asset('fonts/shipping_font/ShipReference.ttf') }}") format('truetype');
    }
  </style>

</head>

<body>
  <img src="{{ secure_asset('storage/template/blank_non_ups.png') }}" style="margin-top: 0px; height: 100%">
  {{--    ship from --}}
  <div
    style="position: absolute; top: 6px; left: 7.5px; line-height: 0.596666em; font-size: 18px; text-transform: uppercase; font-family: ShipMail">
    {{ $data['dataFrom']['name'] }}
    @if (isset($data['dataFrom']['phone']) && $data['dataFrom']['phone'] != '')
      <br>
      {{ $data['dataFrom']['phone'] }}
    @endif
    @if (isset($data['dataFrom']['companyName']) && $data['dataFrom']['companyName'] != '')
      <br>
      {{ $data['dataFrom']['companyName'] }}
    @endif
    @if (isset($data['dataFrom']['address2']) && $data['dataFrom']['address2'] != '')
      <br>
      {{ $data['dataFrom']['address2'] }}
    @endif
    <br>
    {{ $data['dataFrom']['address1'] }}
    <br>
    {{ $data['dataFrom']['city'] }} {{ $data['dataFrom']['state'] }} {{ $data['dataFrom']['zip'] }}
  </div>
  {{-- ship from --}}

  {{-- package --}}
  <div style="position: absolute; top: -3px; right: 156px; font-size: 25px; font-family: ShipToBig">
    <span>{{ $data['package']['weight'] }}</span>
  </div>

  <div style="position: absolute; top: 17.5px; left: 269px; font-size: 20px; font-family: ShipMail">
    {{ $data['package']['length'] }}, {{ $data['package']['width'] }}, {{ $data['package']['height'] }}
  </div>
  {{-- package --}}

  {{--    ship to --}}
  <div
    style="position: absolute; top: 84px; left: 25px; line-height: 0.4416666em; font-size: 32.5px; text-transform: uppercase; font-family: ShipToSmall">
    {{ $data['dataTo']['name'] }}
    @if (isset($data['dataTo']['phone']) && $data['dataTo']['phone'] != '')
      <br>
      {{ $data['dataTo']['phone'] }}
    @endif

    @if (isset($data['dataTo']['companyName']) && $data['dataTo']['companyName'] != '')
      <br>
      {{ $data['dataTo']['companyName'] }}
    @endif
    @if (isset($data['dataTo']['address2']) && $data['dataTo']['address2'] != '')
      <br>
      {{ $data['dataTo']['address2'] }}
    @endif
    <br>
    {{ $data['dataTo']['address1'] }}
    <br>
    <div style="height: 4px"></div>
    <span style="font-family: ShipToBig; font-size: 33.5px">
      {{ $data['dataTo']['city'] }} {{ $data['dataTo']['state'] }} {{ $data['dataTo']['zip'] }}
    </span>
  </div>
  {{--    ship to --}}

  {{--    maxi code --}}
  <div style="position: absolute; top: 204px;">
    @if ($data['package']['weight'] >= 70)
      <div style="position: absolute; top: -40px; left: 18px">
        <span style="font-size: 175px; font-family: ShipState">H</span>
      </div>
    @else
      <img src="{{ secure_asset($barcode) }}" style="margin-left: 7px; margin-top: 0px; height: 27%" alt="maxicode">
    @endif
  </div>
  {{--    maxi code --}}

  {{--    routeCode --}}
  <div style="position: absolute; top: 193px; left: 125px">
    <span style="font-size: 57px; font-family: ShipState">{{ $data['routeCode'] }}</span>
    <div style="margin-top: -2px; margin-left: 10px">{!! DNS1D::getBarcodeHTML('420' . $data['dataTo']['zip'], 'C128', 1.5, 50) !!}</div>
  </div>
  {{--    routeCode --}}

  {{-- serveice name --}}
  <div style="position: absolute; top: 314px; font-size: 25px; left: 3px; font-family: ShipUPS">
    <span style="text-transform: uppercase;">{{ $data['lblservicename'] }}</span>
  </div>
  {{-- tracking no --}}
  <div style="position: absolute; top: 344px; font-size: 14px; margin-left: 86px; font-family: ShipTracking">
    <span>
      {{ substr($data['tracking_no'], 0, 2) }}
      {{ substr($data['tracking_no'], 2, 3) }}
      {{ substr($data['tracking_no'], 5, 3) }}
      {{ substr($data['tracking_no'], 8, 2) }}
      {{ substr($data['tracking_no'], 10, 4) }}
      {{ substr($data['tracking_no'], 14, 4) }}
      {{ substr($data['tracking_no'], 18) }}
    </span>
  </div>
  {{-- tracking no --}}
  @php
    $service = strtolower($data['lblservicename']);
    $isSaturday = $data['package']['saturdayDelivery'] ?? false;
  @endphp

  @if ($service === 'ups ground')
    <div style="position: absolute; left: 320px; top: 312px;">
      <div style="border: 1px solid #000000; width: 59px; height: 53px; background: #000000"></div>
    </div>
  @else
    <div style="position: absolute; left: 302px; top: 303px; font-size: 66px; font-family: ShipSymbol">
      @switch($service)
        @case('next day air')
        @case('ups next day air')
          @if ($isSaturday)
            <span>1</span><span style="margin-left: 9px">S</span>
          @else
            1
          @endif
        @break

        @case('ups next day air early')
          <span>1</span>
          <span style="margin-left: -7px">+</span>
          @if ($isSaturday)
            <span style="margin-left: -12px">S</span>
          @endif
        @break

        @case('ups 2nd day air')
          <span style="margin-left: 10px">2</span>
        @break

        @case('ups next day air saver')
          <span style="margin-left: 5px">1</span><span style="margin-left: 5px">P</span>
        @break
      @endswitch
    </div>
  @endif


  {{-- service name --}}

  <div style="position: absolute; top: 370px; width: 110%; margin: 4px -1px;">
    <div style="margin-top: 1px; margin-left: 25px">{!! DNS1D::getBarcodeHTML($data['tracking_no'], 'C128', 1.7, 97) !!}</div>
  </div>

  <div style="position: absolute; top: 545px; width: 50%; font-family: ShipReference">
    @if ($data['package']['ref1'])
      <div style="margin-left: 5px; font-size: 13px;">Reference No.1: {{ $data['package']['ref1'] }}</div>
    @endif
  </div>
  <div style="position: absolute; top: 565px; left: 28%; width: 100%; font-size: 14px; font-family: ShipReference">
    <div style="position: absolute;">XOL 25.07.14</div>
    <div style="position: absolute; left: 80px;">NV45 24.0A 07/2025*</div>
  </div>
</body>

</html>
