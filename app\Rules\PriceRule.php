<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\DB;

class PriceRule implements ValidationRule
{
	private $param;
	public function __construct($param){
		$this->param = $param;
	}
	
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
	    $sql = "SELECT DISTINCT
                custom_price.price
                FROM services
                INNER JOIN user_service ON services.id = user_service.service_id
                INNER JOIN custom_price ON services.id = custom_price.service_id
                WHERE services.`status` = 1
                AND services.is_deleted <> 1
                AND services.uid = :uid
                AND :weightInputFrom >= custom_price.weight_from AND :weightInputTo <= custom_price.weight_to ";
	    $data = DB::selectOne($sql, [
		    'uid' => $this->param['Class'],
		    'weightInputFrom' => $this->param['Weight'],
		    'weightInputTo' => $this->param['Weight']
	    ]);
	    if (!isset($data->price)) {
		    $fail("Price for weight " . $value . " not found");
	    }
    }
}
