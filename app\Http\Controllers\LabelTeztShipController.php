<?php

namespace App\Http\Controllers;

use DateTime;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class LabelTeztShipController extends Controller
{
    public static function getLabelTeztship(Request $request): JsonResponse|array
    {
        $url_get_label = env('URL_API_LABEL2_TEZTSHIP');

        $data = $request->all();

        $data['user_id'] = 'test';

        $response = Http::post($url_get_label . "/create", $data);

        $dataBody = json_decode($response->body());
        $labelBase64 = $dataBody->label;
        $file_name = '/orders/' . $data['user_id'] . '/' . uniqid() . '.png';
        Storage::disk('public')->put($file_name, base64_decode($labelBase64));

        LabelController::convertImage($file_name);

        // convert image to pdf
        $format_date = date_format(new DateTime(('now')), 'Ymdhms');
        $pdf_file_name = '/orders/' . $data['user_id'] . '/' . uniqid() . '_' . $format_date . '.pdf';

        $data['routeCode'] = $dataBody->routeCode;
        $data['lblservicename'] = $dataBody->lblservicename;
        $data['label_image'] = $file_name;
        $data['tracking_no'] = $dataBody->tracking;
        $data['pkgInfo']['saturdayDelivery'] = $data['satDelivery'];

        PdfController::genPDFLabelTeztship($data, Storage::disk('public')->path($pdf_file_name));

        Storage::disk('public')->delete($file_name);

        DB::connection('mysql_shipease')->table('history_label')
            ->insert([
                'from'          => $data['fromCustomer']['name'],
                'to'            => $data['toCustomer']['name'],
                'service'       => $data['pkgInfo']['serviceName'],
                'tracking'      => $dataBody->tracking,
                'type'          => 'label1',
                'create_at'     => new DateTime(),
                'url_label'     => $pdf_file_name
            ]);

        $dataAddressBook = [];

        if ($data["saveAddressFrom"]) {
            $dataAddressBook[] = [
                "country" => $data['fromCustomer']["country"],
                "name" => $data['fromCustomer']["name"],
                "company_name" => $data['fromCustomer']["companyName"],
                "address1" => $data['fromCustomer']["address1"],
                "address2" => $data['fromCustomer']["address2"],
                "city" => $data['fromCustomer']["city"],
                "state" => $data['fromCustomer']["state"],
                "zipcode" => $data['fromCustomer']["postalCode"],
                "phone" => $data['fromCustomer']["phone"],
            ];
        }

        if ($data["saveAddressTo"]) {
            $dataAddressBook[] = [
                "country" => $data['toCustomer']["countryCode"],
                "name" => $data['toCustomer']["name"],
                "company_name" => $data['toCustomer']["companyName"],
                "address1" => $data['toCustomer']["address1"],
                "address2" => $data['toCustomer']["address2"],
                "city" => $data['toCustomer']["city"],
                "state" => $data['toCustomer']["state"],
                "zipcode" => $data['toCustomer']["postalCode"],
                "phone" => $data['toCustomer']["phone"],
            ];
        }
        DB::connection('mysql_shipease')->table('address_book')->insert($dataAddressBook);
        return [
            'tracking_no' => $dataBody->tracking,
            'file_name' => $pdf_file_name
        ];
    }
}
