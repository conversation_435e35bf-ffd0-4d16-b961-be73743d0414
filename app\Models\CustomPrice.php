<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomPrice extends Model
{
    use HasFactory;

    protected $table = 'custom_price';
    protected $fillable = ['service_id', 'weight_from', 'weight_to', 'price', 'user_id'];

    public function service(): BelongsTo
    {
        return $this->belongsTo(Services::class);
    }
}
