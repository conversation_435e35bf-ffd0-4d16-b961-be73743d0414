<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\UspsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;

class UspsLabelTest extends TestCase
{
    use RefreshDatabase;

    protected UspsService $uspsService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->uspsService = new UspsService();
        
        // Fake storage để test
        Storage::fake('public');
    }

    public function test_create_label_generates_pdf()
    {
        // Dữ liệu test
        $data = [
            'dataFrom' => [
                'name' => 'KC EXPRESS LLC',
                'companyName' => 'KC EXPRESS LLC',
                'address1' => '2624 TOY LN',
                'address2' => '',
                'city' => 'SAN JOSE',
                'state' => 'CA',
                'zip' => '95121',
                'phone' => '(*************'
            ],
            'dataTo' => [
                'name' => 'ARJAY C LAWSON',
                'companyName' => '',
                'address1' => '4433 SAN ERNESTO AVE',
                'address2' => 'UNIT 221B',
                'city' => 'ANCHORAGE',
                'state' => 'AK',
                'zip' => '99508-2852',
                'phone' => '(*************'
            ],
            'package' => [
                'weight' => '32.00'
            ],
            'user_id' => 1
        ];

        $service_info = (object) [
            'name' => 'USPS Ground Advantage',
            'provider' => 'usps_template'
        ];

        // Gọi method createLabel
        $result = $this->uspsService->createLabel($data, $service_info);

        // Kiểm tra kết quả
        $this->assertArrayHasKey('tracking_no', $result);
        $this->assertArrayHasKey('file_name', $result);
        $this->assertNotEmpty($result['tracking_no']);
        $this->assertNotEmpty($result['file_name']);
        
        // Kiểm tra tracking number format
        $this->assertMatchesRegularExpression('/^\d{4} \d{6} \d{4} \d{2}$/', $result['tracking_no']);
        
        // Kiểm tra file được tạo
        $this->assertStringContains('_usps_label.pdf', $result['file_name']);
    }

    public function test_generate_tracking_number_format()
    {
        $reflection = new \ReflectionClass($this->uspsService);
        $method = $reflection->getMethod('generateTrackingNumber');
        $method->setAccessible(true);

        $trackingNumber = $method->invoke($this->uspsService);

        // Kiểm tra format: 9434 123456 1234 12
        $this->assertMatchesRegularExpression('/^\d{4} \d{6} \d{4} \d{2}$/', $trackingNumber);
        $this->assertStringStartsWith('9434', $trackingNumber);
    }

    public function test_template_renders_correctly()
    {
        $labelData = [
            'from' => [
                'name' => 'Test Sender',
                'company' => 'Test Company',
                'address1' => '123 Test St',
                'address2' => '',
                'city' => 'Test City',
                'state' => 'CA',
                'zip' => '12345',
                'phone' => '555-1234'
            ],
            'to' => [
                'name' => 'Test Recipient',
                'company' => '',
                'address1' => '456 Test Ave',
                'address2' => 'Apt 1',
                'city' => 'Test Town',
                'state' => 'NY',
                'zip' => '67890',
                'phone' => '555-5678'
            ],
            'tracking_number' => '9434 123456 1234 56',
            'weight' => '10.00',
            'service_name' => 'USPS Ground Advantage'
        ];

        // Render template
        $html = view('template_pdf.usps_label', ['data' => $labelData])->render();

        // Kiểm tra nội dung template
        $this->assertStringContains('Test Sender', $html);
        $this->assertStringContains('Test Recipient', $html);
        $this->assertStringContains('9434 123456 1234 56', $html);
        $this->assertStringContains('USPS GROUND ADVANTAGE', $html);
        $this->assertStringContains('123 Test St', $html);
        $this->assertStringContains('456 Test Ave', $html);
        $this->assertStringContains('Apt 1', $html);
    }
}
