<?php

namespace App\Http\Helper;

use Dompdf\Dompdf;
use Dompdf\Options;

class PDFHelper
{
    public static function createTemplateFDF(array $size_page = [0, 0, 285, 433], string $orientation = 'portrait'): Dompdf
    {
        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $options->set('margin_top', 0);
        $options->set('margin_bottom', 0);
        $options->set('margin_left', 0);
        $options->set('margin_right', 0);

        // Register the custom font

        // Instantiate Dompdf with options
        $dompdf = new Dompdf($options);

        $dompdf->set_option('tempDir', storage_path('app/tmp'));
        $dompdf->setPaper($size_page, $orientation);
        return $dompdf;
    }
}
