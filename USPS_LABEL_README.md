# USPS Label Generator

## Tổng quan

Chức năng tạo label USPS sử dụng template Blade của <PERSON> và convert thành file PDF với kích thước 4x6 inch, phù hợp với mẫu label USPS Ground Advantage.

## Tính năng

- ✅ Tạo label USPS theo mẫu chuẩn với template Blade
- ✅ Generate tracking number theo format USPS (9434 XXXXXX XXXX XX)
- ✅ Convert HTML template thành PDF với kích thước 4x6 inch
- ✅ Hiển thị thông tin người gửi và người nhận
- ✅ Tạo barcode cho tracking number
- ✅ Lưu file PDF vào storage

## Cách sử dụng

### 1. Trong Controller

```php
// Trong LabelController hoặc controller khác
$data = [
    'dataFrom' => [
        'name' => 'KC EXPRESS LLC',
        'companyName' => 'KC EXPRESS LLC',
        'address1' => '2624 TOY LN',
        'city' => 'SAN JOSE',
        'state' => 'CA',
        'zip' => '95121',
        'phone' => '(*************'
    ],
    'dataTo' => [
        'name' => 'ARJAY C LAWSON',
        'address1' => '4433 SAN ERNESTO AVE',
        'address2' => 'UNIT 221B',
        'city' => 'ANCHORAGE',
        'state' => 'AK',
        'zip' => '99508-2852'
    ],
    'package' => [
        'weight' => '32.00'
    ],
    'user_id' => $userId
];

$service_info = (object) [
    'name' => 'USPS Ground Advantage',
    'provider' => 'usps_template'  // Quan trọng: phải set provider này
];

$uspsService = new UspsService();
$result = $uspsService->createLabel($data, $service_info);

// Kết quả:
// $result['tracking_no'] - Tracking number
// $result['file_name'] - Đường dẫn file PDF
```

### 2. Trong Database

Để sử dụng chức năng này, cần cập nhật bảng `services` hoặc `carrier` với provider `usps_template`:

```sql
UPDATE services SET provider = 'usps_template' WHERE carrier_name = 'USPS' AND service_name = 'Ground Advantage';
```

### 3. Test Routes

Đã tạo sẵn các route test:

- `GET /test-usps-template` - Xem template HTML
- `GET /test-usps-label` - Test tạo label và trả về JSON

## Cấu trúc Files

```
resources/views/template_pdf/usps_label.blade.php  # Template Blade cho USPS label
app/Services/UspsService.php                      # Service chính
tests/Unit/UspsServiceTest.php                    # Unit tests
routes/test.php                                   # Test routes
```

## Template Structure

Template được thiết kế theo mẫu USPS Ground Advantage chuẩn:

1. **Header Section**: Logo "G", thông tin service
2. **From Section**: Thông tin người gửi
3. **To Section**: Thông tin người nhận
4. **Tracking Section**: Barcode và tracking number
5. **Bottom Section**: Mã "AP"

## Kích thước PDF

- Kích thước: 4x6 inch (288x432 points)
- Orientation: Portrait
- Margins: 0

## Dependencies

- `milon/barcode`: Tạo barcode cho tracking number
- `dompdf/dompdf`: Convert HTML thành PDF
- Laravel Blade: Template engine

## Testing

Chạy unit tests:

```bash
php artisan test tests/Unit/UspsServiceTest.php
```

Chạy manual test:

```bash
php test_usps_label.php
```

## Tracking Number Format

Format: `9434 XXXXXX XXXX XX`
- Prefix: 9434 (USPS standard)
- Middle: 6 digits random
- Suffix: 4 digits random  
- End: 2 digits random

## Lưu ý

1. File PDF được lưu trong `storage/app/public/orders/{user_id}/`
2. Template sử dụng CSS absolute positioning để đảm bảo layout chính xác
3. Barcode sử dụng Code 128 format
4. Template responsive với các trường thông tin optional (address2, company, phone)

## Troubleshooting

### Lỗi "Class not found"
Đảm bảo đã import đúng namespace:
```php
use App\Services\UspsService;
```

### Lỗi "View not found"
Kiểm tra file template tại: `resources/views/template_pdf/usps_label.blade.php`

### Lỗi PDF không tạo được
Kiểm tra quyền write của thư mục `storage/app/public/orders/`

### Barcode không hiển thị
Đảm bảo package `milon/barcode` đã được cài đặt:
```bash
composer require milon/barcode
```
