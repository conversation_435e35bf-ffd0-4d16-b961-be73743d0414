<?php
namespace App\Services;

use App\Models\CustomPrice;
use App\Models\Services;
use App\Models\UserService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class PriceService
{
    /**
     * Lấy thông tin giá cước cho người dùng dựa trên các tham số.
     *
     * @param array $data
     * @param int $user_id
     * @return Collection
     */
    public function getDataPriceForUser(array $data, int $user_id)
    {
        // Lấy các dịch vụ tương ứng với carrier_id
        $services = Services::where('carrier_id', $data['carrierId'])
            ->where('status', 1)
            ->where('is_deleted', '<>', 1)
            ->get();

        // Lấy thông tin giá cước
        $prices = CustomPrice::whereIn('service_id', $services->pluck('id'))  // Dùng danh sách service_id
        ->where('weight_from', '<=', $data['weight'])
            ->where('weight_to', '>=', $data['weight'])
            ->where('user_id', $user_id)
            ->get();

        $pricesGrouped = $prices->groupBy('service_id');

        // Map over services and attach corresponding prices directly
        return $services->map(function ($service) use ($pricesGrouped) {
            // Get prices for the current service, if any
            $servicePrices = $pricesGrouped->get($service->id, collect());

            // Flatten the prices and include them directly within the service data
            return $servicePrices->map(function ($price) use ($service) {
                return [
                    'logo' => $service->carrier->logo,           // Logo của nhà cung cấp dịch vụ
                    'serviceId' => $service->id,                 // ID của dịch vụ
                    'serviceName' => $service->service_name,     // Tên dịch vụ
                    'transitDay' => $service->transit_day,       // Số ngày vận chuyển
                    'price' => $price->price,                    // Giá cước
                    'weightFrom' => $price->weight_from,         // Trọng lượng từ
                    'weightTo' => $price->weight_to,             // Trọng lượng đến
                    'transitDate' => '',                         // Cố định là rỗng hoặc tính toán sau
                ];
            });
        })->flatten(1);  // Dùng flatten để biến đổi từ mảng 2 chiều thành mảng 1 chiều
    }
}
