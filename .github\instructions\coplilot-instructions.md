# Hướng dẫn cho GitHub Copilot - Dự án Lara<PERSON> 11

Chào mừng Copilot! 👋 Cảm ơn bạn đã hỗ trợ dự án này. Vui lòng tuân thủ các quy tắc và hướng dẫn dưới đây để đảm bảo code được nhất quán, d<PERSON> bảo trì và tuân theo các phương pháp tốt nhất của Laravel 11.

---

## Tổng quan về Dự án

Đây là một ứng dụng web được xây dựng bằng Laravel 11. Mục tiêu là cung cấp một bộ RESTful API mạnh mẽ để phục vụ cho **hai giao diện người dùng (frontend) riêng biệt**:
1.  **Giao diện cho người dùng cuối (User)**: <PERSON><PERSON><PERSON> cho các hoạt động của người dùng đã đăng nhập.
2.  **Giao diện quản trị (Admin)**: <PERSON><PERSON><PERSON> cho quản trị viên để quản lý hệ thống.
3.  **Chức năng chính là liên kết với các provider khác nhau để tạo label shipping từ các carrier UPS, Fedex, USPS, DHL, etc.**

---

## Công nghệ sử dụng

* **Backend**: Laravel 11
* **PHP**: `^8.2`
* **Database**: MySQL / PostgreSQL
* **API Authentication**: **Laravel Sanctum** (Token-based)
* **Authorization**: **Laravel Policies & Gates**
* **Testing**: Pest / PHPUnit
* **Code Styling**: PSR-12

---

## Cấu trúc Thư mục và Quy ước Đặt tên

* **Controllers (API)**: Để giữ cho code có tổ chức, các controller API phải được tách ra thành các thư mục con cho `Admin` và `User` trong mỗi phiên bản.
    * **Admin Controllers**: `App\Http\Controllers\Api\V1\Admin\PostController`
    * **User Controllers**: `App\Http\Controllers\Api\V1\User\ProfileController`

* **Models**: `App\Models` - Tên là danh từ số ít, `PascalCase`. Ví dụ: `Post`, `User`.

* **Form Requests**: `App\Http\Requests` - Có thể được tái sử dụng giữa Admin và User nếu phù hợp.

* **API Resources**: `App\Http\Resources` - Dùng để chuyển đổi (transform) Model thành JSON. Có thể có các Resource khác nhau cho Admin và User nếu cần hiển thị các dữ liệu khác nhau.
    * Ví dụ: `PostResource` (cho User), `Admin/PostResource` (cho Admin).

---

## ✨ Quy ước cho RESTful API

### 1. Routing và Phân tách vai trò

Tất cả các route API phải được định nghĩa trong `routes/api.php` và phân tách rõ ràng bằng tiền tố (prefix) và middleware.

```php
// routes/api.php
use Illuminate\Support\Facades\Route;

// --- Routes cho Admin ---
// Yêu cầu xác thực và quyền admin
Route::prefix('v1/admin')
    ->middleware(['auth:sanctum', 'is.admin']) // 'is.admin' là một middleware tùy chỉnh
    ->name('api.v1.admin.')
    ->group(function () {
        // Ví dụ: Route::apiResource('users', Admin\UserController::class);
        Route::apiResource('posts', App\Http\Controllers\Api\V1\Admin\PostController::class);
    });

// --- Routes cho User ---
// Chỉ yêu cầu xác thực cơ bản
Route::prefix('v1/user')
    ->middleware('auth:sanctum')
    ->name('api.v1.user.')
    ->group(function () {
        // Ví dụ: Route::get('profile', [User\ProfileController::class, 'show']);
    });

// --- Routes công khai (Public) ---
// Không yêu cầu xác thực
Route::prefix('v1/public')
    ->name('api.v1.public.')
    ->group(function () {
        // Ví dụ: Route::get('posts', [Public\PostController::class, 'index']);
    });

### 2. Cấu trúc JSON Response

Luôn tuân thủ một cấu trúc JSON nhất quán cho mọi response.

* **Response thành công (2xx):**
    ```json
    {
      "success": true,
      "data": { ... } // Đối tượng hoặc mảng các đối tượng
      "message": "Thao tác thành công." // Tùy chọn
    }
    ```

* **Response lỗi (4xx, 5xx):**
    ```json
    {
      "success": false,
      "message": "Đã xảy ra lỗi.",
      "errors": { ... } // Tùy chọn: Chi tiết lỗi validation hoặc các lỗi khác
    }
    ```

### 3. Data Transformation (API Resources)

**KHÔNG BAO GIỜ** trả về Eloquent Model trực tiếp từ Controller. Luôn sử dụng **API Resources** để kiểm soát và định dạng dữ liệu trả về.

* **Ví dụ về `PostResource`**:
    ```php
    // app/Http/Resources/PostResource.php
    <?php

    namespace App\Http\Resources;

    use Illuminate\Http\Request;
    use Illuminate\Http\Resources\Json\JsonResource;

    class PostResource extends JsonResource
    {
        public function toArray(Request $request): array
        {
            return [
                'id' => $this->id,
                'title' => $this->title,
                'slug' => $this->slug,
                'content' => $this->content,
                'created_at' => $this->created_at->toIso8601String(),
                'author' => UserResource::make($this->whenLoaded('user')),
            ];
        }
    }
    ```

### 4. Controller API mẫu

* Controller phải nằm trong namespace `App\Http\Controllers\Api\V1`.
* Sử dụng API Resources để trả về dữ liệu.
* Sử dụng đúng mã trạng thái HTTP.

```php
// app/Http/Controllers/Api/V1/PostController.php
<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\StorePostRequest;
use App\Http\Resources\PostResource;
use App\Models\Post;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class PostController extends Controller
{
    public function index(Request $request)
    {
        // Thêm eager loading và phân trang
        $posts = Post::with('user')->paginate($request->input('per_page', 15));
        return PostResource::collection($posts);
    }

    public function store(StorePostRequest $request): PostResource
    {
        $post = Post::create($request->validated());
        return PostResource::make($post);
    }

    public function show(Post $post): PostResource
    {
        // Route model binding tự động xử lý 404
        return PostResource::make($post->load('user'));
    }

    public function destroy(Post $post): Response
    {
        $post->delete();
        // Trả về response 204 No Content
        return response()->noContent();
    }
}