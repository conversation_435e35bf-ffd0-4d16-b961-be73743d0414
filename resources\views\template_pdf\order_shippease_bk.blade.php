<!DOCTYPE html>
<html>
<head>
    <title>From {{ $data['fromCustomer']['name'] }} To {{ $data['toCustomer']['name'] }}</title>
    <style>
        html {
            margin: 0.5px;
        }
        body {
            padding: 0;
            border-bottom: 1.5px solid #000; /* Add border around the content area */
            border-right: 1.5px solid #000; /* Add border around the content area */
            font-family: AvrileSans-Condensed;
            margin: 0 -1px -1px;
        }
        /* Other CSS styles for your content */

        @font-face {
            font-family: 'AvrileSans-Condensed';
            src: url("{{ asset('css/AvrileSans-Condensed.ttf') }}") format('truetype');
        }
        @font-face {
            font-family: 'Avrile Sans Condensed Bold';
            src: url("{{ asset('css/AvrileSans-CondensedBold.ttf') }}") format('truetype');
        }
    </style>

</head>
<body>
    <div style="position: absolute; top: 1px; left: 8px; line-height: 0.7em; font-size: 11px; text-transform: uppercase;">
        {{ $data['fromCustomer']['name'] }}
        @if(isset($data['fromCustomer']['phone']) && $data['fromCustomer']['phone'] != '')
            <br>
            {{ $data['fromCustomer']['phone'] }}
        @endif
        @if(isset($data['fromCustomer']['companyName']) && $data['fromCustomer']['companyName'] != '')
            <br>
            {{ $data['fromCustomer']['companyName'] }}
        @endif
        @if(isset($data['fromCustomer']['address2']) && $data['fromCustomer']['address2'] != '')
            <br>
            {{ $data['fromCustomer']['address2'] }}
        @endif
        <br>
        {{ $data['fromCustomer']['address1'] }}
        <br>
        {{ $data['fromCustomer']['city'] }} {{ $data['fromCustomer']['state'] }} {{ $data['fromCustomer']['postalCode'] }}
    </div>

    <div style="position: absolute; top: -11px; right: 140px; font-size: 18px; font-family: Avrile Sans Condensed Bold">
       <span>{{ $data['pkgInfo']['weight'] }} LBS</span>
    </div>
    <div style="position: absolute; top: -11px; right: 18px; font-size: 18px; font-family: Avrile Sans Condensed Bold">
        1 OF 1
    </div>
    <div style="position: absolute; top: 12px; right: 80px; font-size: 13px;">
        DWT: {{ $data['pkgInfo']['length'] }}, {{ $data['pkgInfo']['width'] }}, {{ $data['pkgInfo']['height'] }}
    </div>

    <div style="position: absolute; top: 60px; left: 8px; font-family: Avrile Sans Condensed Bold">
        <span style="font-size: 20px">SHIP TO:</span> <br>
    </div>

    <div style="position: absolute; top: 88px; left: 25px; line-height: 0.7em; font-size: 14px; text-transform: uppercase;">
        {{ $data['toCustomer']['name'] }}
        @if(isset($data['toCustomer']['phone']) && $data['toCustomer']['phone'] != '')
            <br>
            {{ $data['toCustomer']['phone'] }}
        @endif

        @if(isset($data['toCustomer']['companyName']) && $data['toCustomer']['companyName'] != '')
            <br>
            {{ $data['toCustomer']['companyName'] }}
        @endif
        @if(isset($data['toCustomer']['address2']) && $data['toCustomer']['address2'] != '')
            <br>
            {{ $data['toCustomer']['address2'] }}
        @endif
        <br>
        {{ $data['toCustomer']['address1'] }}
        <br>
        <div style="height: 3px"></div>
        <span style="font-family: Avrile Sans Condensed Bold; font-size: 22px">
            {{ $data['toCustomer']['city'] }} {{ $data['toCustomer']['state'] }} {{ $data['toCustomer']['postalCode'] }}
        </span>
    </div>



    <div style="border-top: 2.3px solid #000; height: 117px; position: absolute; top: 202px; width: 110%; margin: 0 -1px">
        @if($data['pkgInfo']['weight'] >= 70)
            <div style="width: 120px; text-align: center; height: 113px">
                <div style="margin-top: -97px;">
                    <span style="font-size: 140px; font-family: Avrile Sans Condensed Bold">H</span>
                </div>
            </div>
            <div style="position: absolute; width: 4px; height: 113px; border-right: 2px solid #000; left: 115px; top: -2px"></div>
        @else
            <div style="width: 120px; border-right: 2px solid #000">
                <img src="{{ asset($barcode) }}" style="margin-left: 5px; margin-top: 1px; height: 94%" alt="maxicode">
            </div>
        @endif

        <div style="position: absolute; top: -21px; left: 130px">
           <span style="font-size: 40px; font-family: Avrile Sans Condensed Bold">{{ $data['routeCode'] }}</span>
            <div style="margin-top: -4px; margin-left: 10px">{!! DNS1D::getBarcodeHTML('420' . $data['toCustomer']['postalCode'], "C128",1.5,53) !!}</div>
        </div>
    </div>
    <div style="position: absolute; top: 315px; height: 6px; background: #000; width: 110%; margin: 0 -1px"></div>

    <div style="position: absolute; top: 315px; height: 60px; width: 110%; margin: 0 -1px; border-bottom: 2.3px solid #000; ">
        <div style="position: absolute; margin-top: -8px; font-size: 28px; margin-left: 6px; font-family: Avrile Sans Condensed Bold">
            <span style="text-transform: uppercase;">{{ $data['lblservicename'] }}</span>
        </div>
        <div style="position: absolute; margin-top: 30px; font-size: 16px; margin-left: 6px">
            <span>TRACKING #:
                {{ substr($data['tracking_no'], 0, 2) }}
                {{ substr($data['tracking_no'], 2, 3) }}
                {{ substr($data['tracking_no'], 5, 3) }}
                {{ substr($data['tracking_no'], 8, 2) }}
                {{ substr($data['tracking_no'], 10, 4) }}
                {{ substr($data['tracking_no'], 14, 4) }}
                {{ substr($data['tracking_no'], 18) }}
            </span>
        </div>

        <div style="position: absolute; right: 50px; top: -19px; font-size: 47px; font-family: Avrile Sans Condensed Bold">
            <span>
                @if(strtolower($data['lblservicename']) == 'next day air' || strtolower($data['lblservicename']) == 'ups next day air')
                    @if( $data['pkgInfo']['saturdayDelivery'])
                        1S
                    @else
                        1
                    @endif
                @endif
                @if(strtolower($data['lblservicename']) == 'ups next day air early')
                    @if( $data['pkgInfo']['saturdayDelivery'])
                        1+S
                    @else
                        1+
                    @endif
                @endif
            </span>
        </div>
    </div>

    <div style="position: absolute; top: 380px; height: 112px; width: 110%; margin: 0 -1px;">
        <div style="margin-top: 5px; margin-left: 25px">{!! DNS1D::getBarcodeHTML($data['tracking_no'], "C128",1.5,100) !!}</div>
    </div>
    <div style="position: absolute; top: 493px; height: 6px; background: #000; width: 110%; margin: 0 -1px"></div>
    <div style="position: absolute; top: 495px; width: 110%">
        <div style="margin-left: 3px; font-size: 11px;">BILLING: P/P</div>
    </div>

    <div style="position: absolute; top: 548px; width: 50%">
        @if($data['pkgInfo']['reference1'])
            <div style="margin-left: 3px; font-size: 11px;">Reference No.1: {{ $data['pkgInfo']['reference1'] }}</div>
        @endif
    </div>
    <div style="position: absolute; top: 562px; left: 28%; width: 100%; font-size: 8px">
        <div style="position: absolute;">XOL 24.04.04</div>
        <div style="position: absolute; left: 80px;">NV45 15.0A 04/2024*</div>
    </div>

    <div style="position: absolute; top: 540px; right: 55px;">
        <img src="{{ asset('storage/template/gift.svg') }}" style="height: 30px" alt="gift">
    </div>
</body>
</html>
