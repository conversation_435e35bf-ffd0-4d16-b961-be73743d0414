<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Carrier extends Model
{
    use HasFactory;
    protected $fillable = ['logo', 'carrier_name'];

    protected $table = 'carrier';

    public function services(): HasMany
    {
        return $this->hasMany(Services::class);
    }
}
