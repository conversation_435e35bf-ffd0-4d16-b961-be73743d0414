<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserService extends Model
{
    use HasFactory;

    protected $table = 'user_service';

    protected $fillable = ['service_id', 'user_id', 'is_enable'];
    public function service(): BelongsTo
    {
        return $this->belongsTo(Services::class);
    }
}
