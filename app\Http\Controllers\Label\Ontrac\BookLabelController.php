<?php

namespace App\Http\Controllers\Label\Ontrac;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class BookLabelController extends Controller
{
  private const STATUS_ORDER_CANCEL = 3;
  private const STATUS_ORDER_DONE = 1;
  private const STATUS_ORDER_REFUND = 2;

  public function bookLabel(Request $request)
  {
    $data = $request->all();
    $data['user_id'] = $request->user()->id;

    $companyRecive = $data['dataRecipient']['companyName'];
    $contactRecive = $data['dataRecipient']['name'];
    $shipFrom = $data['dataSender']['addressSelected']['title'];
    $address = $data['dataRecipient'];
    $dimensions = $data['dataPackage'];
    $sender = $data['dataSender'];

    for ($i = 1; $i <= $data['dataPackage']['boxCount']; $i++) {
      $new_date = new \DateTime(('now'));
      // create order
      $order_id = uniqid() . '_' . date_format($new_date, 'YMdhms');
      $listOrderId[] = $order_id;

      DB::table('orders')->insert([
        'id' => $order_id,
        'date_create' => $new_date,
        'from_name' => $shipFrom,
        'to_name' => $contactRecive,
        'no_of_package' => $data['dataPackage']['boxCount'],
        'weight' => $dimensions['weight'],
        'length' => $dimensions['length'],
        'width' => $dimensions['width'],
        'height' => $dimensions['height'],
        'saturday_delivery' => 0,
        'signature' => 0,
        'service_id' => 'ontrac',
        'to_company' => $companyRecive,
        'to_phone' => $address['phone'],
        'to_country' => 'US',
        'to_address1' => $address['address1'],
        'to_address2' => $address['address2'],
        'to_zip' => $address['zip'],
        'to_city' => $address['city'],
        'to_state' => $address['state'],
        'from_company' => $sender['companyName'],
        'from_country' => 'US',
        'from_phone' => $sender['phone'],
        'from_address1' => $sender['address1'],
        'from_address2' => $sender['address2'] ?? null,
        'from_zip' => $sender['zip'],
        'from_city' => $sender['city'],
        'from_state' => $sender['state'],
        'status' => self::STATUS_ORDER_CANCEL,
        'price' => $data['price'],
        'user_id' => $data['user_id'],
        'tracking_status' => 'Pre_transit',
        'create_at' => $new_date,
        'delivery_date' => $data['transit_date'],
        'discount' => 0,
        'total_price' => $data['price'] * $data['dataPackage']['boxCount']
      ]);
    }

    $labelImage = [];
    foreach ($listOrderId as $item) {
      $rs = $this->processCreateLabel($data);

      if ($rs['tracking_no'] == '') {
        return response()->json([
          'status' => 422,
          'message' => $rs['Error']
        ]);
      }

      $labelImage[] = $rs['file_name'];

      $dataUpdate = [
        'status' => self::STATUS_ORDER_DONE,
        'tracking_id' => $rs['tracking_no'],
        'label_image' => $rs['file_name']
      ];

      if ($rs['DeliverTo']) {
        $dataUpdate['to_name'] = $rs['DeliverTo']['Contact'];
        $dataUpdate['to_address1'] = $rs['DeliverTo']['StreetAddress'];
        $dataUpdate['to_address2'] = $rs['DeliverTo']['Address2'];
        $dataUpdate['to_zip'] = $rs['DeliverTo']['PostalCode'];
        $dataUpdate['to_city'] = $rs['DeliverTo']['City'];
        $dataUpdate['to_state'] = $rs['DeliverTo']['State'];
      }

      // update order
      DB::table('orders')->where('id', '=', $item)
        ->update($dataUpdate);
    }

    return response()->json([
      'status' => 200,
      'current_balance' => 0,
      'data' => $labelImage
    ]);
  }

  public function getRateLabelOntrac(Request $request) {
    $data = $request->all();

    $shipFrom = $data['dataSender']['addressSelected']['title'];
    $address = $data['dataRecipient'];
    $dimensions = $data['dataPackage'];

    $customerBranchMap = [
      'San Jose' => 'KCEXSJCA',
      'Houston' => 'KCEXHNTX',
      'Fountain Valley' => 'KCEXFVCA',
    ];
    $localTenderTime = now()->addDays(1)->setTime(17, 0)->toIso8601ZuluString();

    $payload = [
      'CustomerBranch' => $customerBranchMap[$shipFrom] ?? 'KCEXSJCA',
      'ThirdPartyBillingAccount' => '',
      'InjectionFacilityCode' => '',
      'TenderDateTime' => $localTenderTime,
      'DeliverTo' => [
        'StreetAddress' => $address['address1'] ?? '',
        'Address2' => $address['address2'] ?? '',
        'PostalCode' => $address['zip'] ?? '',
        'City' => $address['city'] ?? '',
        'State' => $address['state'] ?? '',
        'ISOCountryCode' => 'US',
      ],
      'Pieces' => [
        [
          'ContainerType' => 'CustomPackaging',
          'Weight' => $dimensions['weight'],
          'WeightUnitOfMeasurement' => 'lbs',
          'Width' => $dimensions['width'] ?? 0,
          'Length' => $dimensions['length'] ?? 0,
          'Height' => $dimensions['height'] ?? 0,
          'UnitOfMeasurement' => 'in',
          'Attributes' => [],
        ]
      ]
    ];

    $wsid = env('WSID');
    $wskey = env('WSKEY');

    $url = "https://ws.ontrac.com/Method/ServicesAndCharges/v3/json/{$wsid}/{$wskey}";

    $response = Http::withHeaders([
      'Content-Type' => 'application/json'
    ])->withOptions(['verify' => false])
      ->post($url, $payload);

    return $response->json();
  }

  public function processCreateLabel($data): array
  {
    try {
      $companyRecive = $data['dataRecipient']['companyName'] ?? '';
      $contactRecive = $data['dataRecipient']['name'];
      $shipFrom = $data['dataSender']['addressSelected']['title'];
      $address = $data['dataRecipient'];
      $dimensions = $data['dataPackage'];

      $customerBranchMap = [
        'San Jose' => 'KCEXSJCA',
        'Houston' => 'KCEXHNTX',
        'Fountain Valley' => 'KCEXFVCA',
      ];

      $tenderLocations = [
        'San Jose' => [
          'Contact' => 'Warehouse Manager',
          'Company' => 'OnTrac',
          'StreetAddress' => '1121 Montague Expy',
          'Address2' => '',
          'PostalCode' => '95035-6845',
          'City' => 'Milpitas',
          'State' => 'CA',
        ],
        'Houston' => [
          'Contact' => 'Warehouse Manager',
          'Company' => 'OnTrac',
          'StreetAddress' => '11311 FM 529 RD',
          'Address2' => '',
          'PostalCode' => '77041',
          'City' => 'HOUSTON',
          'State' => 'TX',
        ],
        'Fountain Valley' => [
          'Contact' => 'Warehouse Manager',
          'Company' => 'OnTrac',
          'StreetAddress' => '10800 ORANGEWOOD AVE',
          'Address2' => '',
          'PostalCode' => '92840',
          'City' => 'GARDEN GROVE',
          'State' => 'CA',
        ],
      ];

      $localTenderTime = now()->addDays(1)->setTime(17, 0)->toIso8601ZuluString();

      $expectedDelivery = now()->addDays(3)->setTime(17, 0)->toIso8601ZuluString();

      $orderPayload = [
        'CustomerBranch' => $customerBranchMap[$shipFrom] ?? 'KCEXSJCA',
        'CustomerOrderNumber' => 'LK' . now()->timestamp,
        'Reference1' => 'REF-' . now()->timestamp,
        'Reference2' => '',
        'ServiceCode' => 'GRND',
        'PickupType' => 'None',
        'ThirdPartyBillingAccount' => '',
        'ExpectedDepartureDateTime' => '',
        'TenderDateTime' => $localTenderTime,
        'TenderAt' => array_merge(
          $tenderLocations[$shipFrom] ?? [],
          [
            'ISOCountryCode' => 'US',
            'Phone' => '',
            'SpecialInstructions' => ''
          ]
        ),
        'DeliverTo' => [
          'Contact' => $contactRecive,
          'Company' => $companyRecive,
          'StreetAddress' => $address['address1'] ?? '',
          'Address2' => $address['address2'] ?? '',
          'PostalCode' => $address['zip'] ?? '',
          'City' => $address['city'] ?? '',
          'State' => $address['state'] ?? '',
          'ISOCountryCode' => 'US',
          'Phone' => $address['phone'] ?? '',
          'Email' => '',
          'SpecialInstructions' => 'Leave at door',
          'ExpectedDeliveryBy' => $expectedDelivery,
        ],
        'Pieces' => [
          [
            'ContainerType' => 'CustomPackaging',
            'Weight' => $dimensions['weight'],
            'WeightUnitOfMeasurement' => 'lbs',
            'Width' => $dimensions['width'] ?? 0,
            'Length' => $dimensions['length'] ?? 0,
            'Height' => $dimensions['height'] ?? 0,
            'UnitOfMeasurement' => 'in',
            'Description' => '',
            'Reference' => '',
            'ExpirationDate' => now()->addDays(20)->toIso8601ZuluString(),
            'Attributes' => [],
          ]
        ]
      ];

      $wsid = env('WSID');
      $wskey = env('WSKEY');
      $env = env('WSENV', 1);

      if (!$wsid || !$wskey) {
        Log::debug('Non get wsid or wskey from env');
        return [
          'Error' => 'Non key or wsid',
          'tracking_no' => '',
          'file_name' => null
        ];
      }

      $url = "https://ws.ontrac.com/Method/PlaceOrder/v3/json/{$wsid}/{$wskey}/{$env}/1/P4x6";

      $response = Http::withHeaders([
        'Content-Type' => 'application/json'
      ])
       ->withOptions(['verify' => false])
        ->post($url, $orderPayload);

      if (!$response->ok()) {
        Log::debug('cannot create order ontrac. Response: ', ['response' => $response->body(), 'body' => $orderPayload]);
        return [
          'Error' => "Error: " . json_decode($response->body())->ErrorMessage,
          'tracking_no' => '',
          'file_name' => null
        ];
      }

      $dataRs = $response->json();

      $tracking_no = $dataRs['Order']['Pieces'][0]['Barcode'];
      $labelBase64 = $dataRs['Order']['Labels'];
      $file_name = '/orders/' . $data['user_id'] . '/' . uniqid() . '.pdf';

      Storage::disk('public')->put($file_name, base64_decode($labelBase64));
      return [
        'tracking_no' => $tracking_no,
        'file_name' => $file_name,
        'DeliverTo' => $dataRs['Order']['DeliverTo'],
      ];
    } catch (\Throwable $e) {
      Log::debug('Cannot create order ontrac. Error: ', ['error' => $e->getMessage(), 'body' => $orderPayload]);;
      return [
        'Error' => $e->getMessage(),
        'tracking_no' => '',
        'file_name' => null
      ];
    }
  }
}
