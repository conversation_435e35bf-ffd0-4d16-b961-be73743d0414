<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;


class UspsService
{

    private $apiUrl;

    public function __construct()
    {
        $this->apiUrl = env('ZAP_URL_API', 'https://api.bulkzsp.com/api/v3');
    }

    public function getLabel($data, $service_info, $order_id)
    {
        // check balance
        $url_api = $this->apiUrl . '/order/check-balance';

        $response = Http::withHeaders([
            'x-api-key' => env('ZAP_API_KEY'),
            'x-client-domain' => 'crushippers.com'
        ])->get($url_api);

        if (!$response->ok()) {
            return [
                'tracking_no' => '',
                'file_name' => null
            ];
        }

        if ($response->json()['balance'] < 0) {
            return [
                'tracking_no' => '',
                'file_name' => null
            ];
        }

        // create order
        $dataPackage = $data['package'];
        $dataFrom = $data['dataFrom'];
        $dataTo = $data['dataTo'];

        $dataBookLabel = [
            "extOrderId" => $order_id,
            "labelType" => $service_info->uid,
            "Weight" => $dataPackage['weight'] ?? 0,
            "Height" => $dataPackage['height'] ?? 0,
            "Length" => $dataPackage['length'] ?? 0,
            "Width" => $dataPackage['width'] ?? 0,
            "callbackUrl" => "https://stgapi.crushippers.com/api/callback",
            "FromCountry" => "US",
            "FromName" => $dataFrom['name'] ?? '',
            "FromCompany" => $dataFrom['companyName'] ?? '',
            "FromPhone" => $dataFrom['phone'] ?? '',
            "FromStreet" => $dataFrom['address1'] ?? '',
            "FromStreet2" => $dataFrom['address2'] ?? '',
            "FromCity" => $dataFrom['city'] ?? '',
            "FromState" => $dataFrom['state'] ?? '',
            "FromZip" => $dataFrom['zip'] ?? '',
            "ToCountry" => "US",
            "ToName" => $dataTo['name'] ?? '',
            "ToCompany" => $dataTo['companyName'] ?? '',
            "ToPhone" => $dataTo['phone'] ?? '',
            "ToStreet" => $dataTo['address1'] ?? '',
            "ToStreet2" => $dataTo['address2'] ?? '',
            "ToCity" => $dataTo['city'] ?? '',
            "ToState" => $dataTo['state'] ?? '',
            "ToZip" => $dataTo['zip'] ?? ''
        ];

        $url_api = $this->apiUrl . '/order/create-order';
        $response = Http::withHeaders([
            'x-api-key' => env('ZAP_API_KEY'),
            'x-client-domain' => 'crushippers.com'
        ])->post($url_api, $dataBookLabel);

        if (!$response->ok()) {
            return [
                'tracking_no' => '',
                'file_name' => null
            ];
        }

        $rs = $response->json();
        if ($rs['type'] != 'success') {
            return [
                'tracking_no' => '',
                'file_name' => null
            ];
        }

        [$tracking_no, $url_label, $id] = $this->retryGetLable($order_id);

        $file_name = '/orders/' . $data['user_id'] . '/' . uniqid() . '.pdf';
        if ($url_label) {
            $url_label = str_replace('./', '/', $url_label);
            $url_download_pdf = 'https://api.bulkzsp.com' . $url_label;
            $pdfResponse = Http::get($url_download_pdf);
            if ($pdfResponse->ok()) {
                Storage::disk('public')->put($file_name, $pdfResponse->body());
            }
        }
        if ($tracking_no == null) {
            return [
                'tracking_no' => '',
                'file_name' => null
            ];
        }

        return [
            'tracking_no' => $tracking_no,
            'file_name' => $file_name,
            '_id' => $id
        ];

    }

    private function retryGetLable($extOrderID) {
        $url_api = $this->apiUrl . '/order/read-orders';
        $tracking_no = null;
        $url_label = null;
        $id = null;

        $maxRetry = 5;
        $retry = 0;
        $found = false;

        while ($retry < $maxRetry && $found == false) {
            $response = Http::withHeaders([
                'x-api-key' => env('ZAP_API_KEY'),
                'x-client-domain' => 'crushippers.com'
            ])->get($url_api);

            if (!$response->ok()) {
                return [null, null, null];
            }

            $lstOrder = $response->json();
            foreach ($lstOrder['orders'] as $item) {
                if ($item['extOrderId'] == $extOrderID) {
                    $tracking_no = $item['tracking'];
                    $url_label = $item['pdf'];
                    $id = $item['_id'];
                    $found = true;
                    break;
                }
            }
            if ($tracking_no) {
                break;
            }
            $retry++;
            // Có thể sleep 1s giữa các lần retry nếu cần
            usleep(500000); // 0.5s
        }
        return [$tracking_no, $url_label, $id];
    }

    public function getRate($data, $carrier)
    {
        $weight = $data['weight'];

        $url_api = $this->apiUrl . '/labeltype/read-types';
        $response = Http::withHeaders([
            'x-api-key' => env('ZAP_API_KEY'),
            'x-client-domain' => 'crushippers.com'
        ])->get($url_api);

        if ($response->ok()) {
            $items = $response->json();
            $result = [];
            foreach ($items as $item) {
                $name = trim($item['name']);
                if (strpos($name, 'USPS') === 0 &&
                    $weight <= $item['maxWeight'] &&
                    isset($item['prices'][0]) &&
                    $weight >= $item['prices'][0]['fromWeight'] &&
                    $weight <= $item['prices'][0]['toWeight']) {
                    $priceObj = $item['prices'][0];

                    $service_db = DB::table('services')->where('uid', '=', $item['uid'])->first();
                    $service_id = null;
                    if ($service_db) {
                        $service_id = $service_db->id;
                    } else {
                        $service_id = DB::table('services')->insertGetId([
                            'carrier_id' => $carrier->id,
                            'service_name' => $name,
                            'service_data_name' => $name,
                            'max_weight' => $item['maxWeight'],
                            'provider' => 'bulkzsp',
                            'uid' => $item['uid'],
                            'transit_day' => $item['transit_day'] ?? 2,
                            'status' => 1,
                            'is_deleted' => 0,
                            'create_at' => new \DateTime('now')
                        ]);
                    }

                    $result[] = [
                        'logo' => $carrier->logo, // carrier_logo,
                        'services_id' => $service_id,
                        'service_name' => $name,
                        'transit_day' => isset($item['transit_day']) ? $item['transit_day'] : 2,
                        'price' => $priceObj['price'],
                        'weight_to' => $priceObj['toWeight'],
                        'weight_from' => $priceObj['fromWeight'],
                        'transit_date' => isset($item['transit_date']) ? $item['transit_date'] : ''
                    ];
                }
            }
            return $result;
        }

        return [];
    }

    public function createLabel($data, $service_info) {
        // Chuẩn bị dữ liệu cho template
        $labelData = [
            'from' => [
                'name' => $data['dataFrom']['name'] ?? '',
                'company' => $data['dataFrom']['companyName'] ?? '',
                'address1' => $data['dataFrom']['address1'] ?? '',
                'address2' => $data['dataFrom']['address2'] ?? '',
                'city' => $data['dataFrom']['city'] ?? '',
                'state' => $data['dataFrom']['state'] ?? '',
                'zip' => $data['dataFrom']['zip'] ?? '',
                'phone' => $data['dataFrom']['phone'] ?? ''
            ],
            'to' => [
                'name' => $data['dataTo']['name'] ?? '',
                'company' => $data['dataTo']['companyName'] ?? '',
                'address1' => $data['dataTo']['address1'] ?? '',
                'address2' => $data['dataTo']['address2'] ?? '',
                'city' => $data['dataTo']['city'] ?? '',
                'state' => $data['dataTo']['state'] ?? '',
                'zip' => $data['dataTo']['zip'] ?? '',
                'phone' => $data['dataTo']['phone'] ?? ''
            ],
            'tracking_number' => $this->generateTrackingNumber(),
            'weight' => $data['package']['weight'] ?? '0.00',
            'service_name' => $service_info->name ?? 'USPS Ground Advantage'
        ];

        // Tạo PDF từ template Blade
        $pdfPath = $this->generatePdfFromTemplate($labelData, $data['user_id']);

        return [
            'tracking_no' => $labelData['tracking_number'],
            'file_name' => $pdfPath
        ];
    }

    /**
     * Generate a mock USPS tracking number
     */
    private function generateTrackingNumber(): string
    {
        // USPS tracking numbers typically start with specific prefixes
        // For demo purposes, we'll generate a realistic-looking number
        $prefix = '9434'; // Common USPS prefix
        $middle = str_pad(rand(100000, 999999), 6, '0', STR_PAD_LEFT);
        $suffix = str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
        $end = str_pad(rand(10, 99), 2, '0', STR_PAD_LEFT);

        return $prefix . ' ' . $middle . ' ' . $suffix . ' ' . $end;
    }

    /**
     * Generate PDF from Blade template
     */
    private function generatePdfFromTemplate($labelData, $userId): string
    {
        // Tạo tên file PDF
        $fileName = '/orders/' . $userId . '/' . uniqid() . '_usps_label.pdf';
        $fullPath = storage_path('app/public' . $fileName);

        // Tạo thư mục nếu chưa tồn tại
        $directory = dirname($fullPath);
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }

        // Sử dụng PDFHelper để tạo PDF với kích thước 4x6 inch
        $dompdf = \App\Http\Helper\PDFHelper::createTemplateFDF([0, 0, 288, 432], 'portrait'); // 4x6 inch in points

        // Render template Blade
        $html = view('template_pdf.usps_label', ['data' => $labelData])->render();

        // Load HTML và render PDF
        $dompdf->loadHtml($html);
        $dompdf->render();

        // Lưu PDF
        file_put_contents($fullPath, $dompdf->output());

        return $fileName;
    }
}
