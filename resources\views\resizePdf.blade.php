<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload File</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 min-h-screen flex items-center justify-center">

<div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
    <h2 class="text-3xl font-bold mb-6 text-gray-800 text-center">Upload an pdf</h2>
    <form action="{{ route('resize.pdf') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
        @csrf
        <div>
            <label for="image" class="block text-sm font-medium text-gray-700 mb-2">Choose an pdf file</label>
            <input type="file" name="pdf" id="pdf" required class="mt-1 block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 p-2.5">
        </div>
        <button type="submit" class="w-full bg-gradient-to-r from-purple-500 via-pink-600 to-red-500 hover:from-purple-600 hover:via-pink-700 hover:to-red-600 text-white font-bold py-3 px-4 rounded-lg shadow-lg transform transition-transform hover:scale-105">
            Upload and Transform
        </button>
    </form>
</div>

</body>
</html>
