<?php

namespace App\Http\Controllers;

use App\Services\TelegramService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use DateTime;
use Intervention\Image\ImageManagerStatic as Image;
use Telegram\Bot\Exceptions\TelegramSDKException;
use App\Services\UspsService;

class LabelController extends Controller
{
    private const STATUS_ORDER_CANCEL = 3;
    private const STATUS_ORDER_DONE = 1;
    private const STATUS_ORDER_REFUND = 2;

    private const STATUS_REFUND_PENDING = 2;
    private const STATUS_REFUND_ACCEPTED = 1;
    private const STATUS_REFUND_REJECTED = 0;

    private const STATUS_TRANSACTION_BALANCE_ADD = 1;
    private const STATUS_TRANSACTION_BALANCE_MINUS = 3;
    private const STATUS_TRANSACTION_ORDER = 2;
    private const STATUS_TRANSACTION_REFUND = 4;
    protected TelegramService $telegramService;
    protected UspsService $uspsService;

    public function __construct(TelegramService $telegramService, UspsService $uspsService)
    {
        $this->telegramService = $telegramService;
        $this->uspsService = $uspsService;
    }

    public function initCreateLabel(Request $request): JsonResponse
    {
        $query_params = $request->all();

        $query_params['user_id'] = $request->user()->id;
        $query = DB::table('carrier');
        if (isset($query_params['status'])) {
            $query->where('status', '=', $query_params['status']);
        }
        $carrier = $query->get();

        // get data address book
        $addressBook = DB::table('address_book')
            ->where('user_id', '=', $query_params['user_id'])
            ->get();

        $package = DB::table('package_save')
            ->where('user_id', '=', $query_params['user_id'])
            ->get();

        return response()->json([
            'carrier' => $carrier,
            'holiday' => DB::table('holiday')->get(),
            'addressBook' => $addressBook,
            'package' => $package,
            'message' => ''
        ]);
    }

    /**
     * @throws TelegramSDKException
     */
    public function createLabel2(Request $request): JsonResponse
    {
        $data = $request->all();

        $data['user_id'] = $request->user()->id;

        // check limit time
        $settings_limit = DB::table('settings')->where('order_no', '=', 4)->first();
        $limit_time = $settings_limit->value ?? 0;

        $sql_checkExecute = "select * from user_limit WHERE user_id = :user_id
		and NOW() < DATE_ADD(create_at,INTERVAL :limit_time SECOND)";

        $user_limit = DB::select($sql_checkExecute, [
            'user_id' => $data['user_id'],
            'limit_time' => $limit_time
        ]);

        if (count($user_limit) > 0) {
            return response()->json([
                'status' => 422,
                'message' => 'Cannot create label. Please try again ' . $limit_time . '(s)'
            ]);
        }

        // check balance
        $userInfo = DB::table('users')->where('id', '=', $data['user_id'])->first();
        if (!isset($userInfo)) {
            return response()->json([
                'status' => false,
                'data' => ''
            ]);
        }
        $current_balance = $userInfo->balance;

        $price_unit = $data['price'] - ($data['price'] * (($data['discount'] ?? 0) / 100));

        if (($price_unit * $data['package']['noOfPackage']) > $current_balance) {
            return response()->json([
                'status' => 422,
                'message' => 'Not enough money'
            ]);
        }

        $service_info = DB::table('services')->where('id', '=', $data['service_id'])->first();

        if (!$service_info) {
            return response()->json([
                'status' => 422,
                'message' => 'Service not found'
            ]);
        }

        $listOrderId = [];

        for ($i = 1; $i <= $data['package']['noOfPackage']; $i++) {
            $new_date = new DateTime(('now'));
            // create order
            $order_id = uniqid() . '_' . date_format($new_date, 'YMdhms');
            $listOrderId[] = $order_id;

            DB::table('orders')->insert([
                'id' => $order_id,
                'date_create' => $new_date,
                'from_name' => $data['dataFrom']['name'],
                'to_name' => $data['dataTo']['name'],
                'no_of_package' => $data['package']['noOfPackage'],
                'weight' => $data['package']['weight'],
                'length' => $data['package']['length'],
                'width' => $data['package']['width'],
                'height' => $data['package']['height'],
                'ref1' => $data['package']['ref1'] ?? '',
                'ref2' => $data['package']['ref2'] ?? '',
                'saturday_delivery' => $data['package']['saturdayDelivery'] ? 1 : 0,
                'signature' => $data['package']['signature'] ? 1 : 0,
                'service_id' => $data['service_id'],
                'to_company' => $data['dataTo']['companyName'],
                'to_phone' => $data['dataTo']['phone'],
                'to_country' => 'US',
                'to_address1' => $data['dataTo']['address1'],
                'to_address2' => $data['dataTo']['address2'],
                'to_zip' => $data['dataTo']['zip'],
                'to_city' => $data['dataTo']['city'],
                'to_state' => $data['dataTo']['state'],
                'from_company' => $data['dataFrom']['companyName'],
                'from_country' => 'US',
                'from_phone' => $data['dataFrom']['phone'],
                'from_address1' => $data['dataFrom']['address1'],
                'from_address2' => $data['dataFrom']['address2'],
                'from_zip' => $data['dataFrom']['zip'],
                'from_city' => $data['dataFrom']['city'],
                'from_state' => $data['dataFrom']['state'],
                'status' => self::STATUS_ORDER_CANCEL,
                'price' => $data['price'],
                'user_id' => $data['user_id'],
                'tracking_status' => 'Pre_transit',
                'create_at' => $new_date,
                'delivery_date' => $data['transit_date'],
                'discount' => $data['discount'] ?? 0,
                'total_price' => $price_unit
            ]);
        }

        $labelImage = [];
        foreach ($listOrderId as $item) {
            if ($service_info->provider == 'pirreas.com') {
                $rs = $this->getLabelPirreas($data, $service_info);
            } else if ($service_info->provider == 'api_python') {
                $rs = $this->getLabelPython($data, $service_info);
            } else if ($service_info->provider == 'bulkzsp') {
                $rs = $this->uspsService->getLabel($data, $service_info, $order_id);
            } else if ($service_info->provider == 'usps_template') {
                $rs = $this->uspsService->createLabel($data, $service_info);
            } else {
                $rs = $this->getLabelApi2($data);
            }

            if ($rs['tracking_no'] == '') {
                return response()->json([
                    'status' => 422,
                    'message' => 'Cannot create label. Please try again'
                ]);
            }

            $labelImage[] = $rs['file_name'];

            // update current balance
            $after_balance = $current_balance - $data['price'];
            DB::table('users')
                ->where('id', '=', $data['user_id'])
                ->update(['balance' => $after_balance]);

            $current_balance = $after_balance;
            $new_date = new DateTime(('now'));
            DB::table('transaction')->insert([
                'id' => uniqid('TRANS_') . date_format($new_date, 'YMdhms'),
                'type' => self::STATUS_TRANSACTION_ORDER,
                'user_id' => $data['user_id'],
                'amount' => -$price_unit,
                'current_amount' => $after_balance,
                'note' => 'Order created successfully.' . PHP_EOL . ' Order ID: ' . $item,
                'create_at' => $new_date,
                'date_create' => $new_date
            ]);

            // update order
            DB::table('orders')->where('id', '=', $item)
                ->update([
                    'status' => self::STATUS_ORDER_DONE,
                    'tracking_id' => $rs['tracking_no'],
                    'label_image' => $rs['file_name'],
                    '_id' => $rs['_id'] ?? ''
                ]);

            // send msg
            $msg = "Book Label success." . PHP_EOL . "Tracking number: " . $rs['tracking_no'];
            $this->sendMessage($msg);
        }

        // save address book
        try {
            $isSaveDefault = false;
            if ($data['dataFrom']['saveAddressDefault']) {
                DB::table('address_book')
                    ->where('user_id', '=', $data['user_id'])
                    ->update([
                        'is_default' => 0
                    ]);
                $this->saveAddressBook($data['dataFrom'], $data['user_id']);
                $isSaveDefault = true;
            }
            if ($data['dataFrom']['saveAddress'] && !$isSaveDefault) {
                $this->saveAddressBook($data['dataFrom'], $data['user_id']);
            }
            if ($data['dataTo'] && $data['dataTo']['saveAddress']) {
                $this->saveAddressBook($data['dataTo'], $data['user_id']);
            }

            // save package
            if (isset($data['package']) && $data['package']['savePackage']) {
                DB::table('package_save')->insert([
                    'user_id' => $data['user_id'],
                    'package_name' => $data['package']['namePackageSaved'],
                    'no_of_package' => $data['package']['noOfPackage'],
                    'weight' => $data['package']['weight'],
                    'length' => $data['package']['length'],
                    'width' => $data['package']['width'],
                    'height' => $data['package']['height'],
                    'ref1' => $data['package']['ref1'] ?? '',
                    'ref2' => $data['package']['ref2'] ?? '',
                    'create_at' => $new_date
                ]);
            }
        } catch (Exception $e) {
            return response()->json([
                'status' => 422,
                'message' => $e->getMessage()
            ]);
        }
        return response()->json([
            'status' => 200,
            'current_balance' => $current_balance,
            'data' => $labelImage
        ]);
    }

    private function saveAddressBook($data, $user_id): void
    {
        DB::table('address_book')->insert([
            'name' => $data['name'],
            'user_id' => $user_id,
            'company_name' => $data['companyName'],
            'phone_number' => $data['phone'],
            'country' => 'US',
            'address1' => $data['address1'],
            'address2' => $data['address2'],
            'zip' => $data['zip'],
            'city' => $data['city'],
            'state' => $data['state'],
            'is_default' => $data['saveAddressDefault'] ? 1 : 0,
            'create_at' => new DateTime(('now')),
        ]);
    }

    public static function convertImage($file_name): void
    {

        $url = Storage::disk('public')->path($file_name);

        // Load the image using Intervention/Image package
        $image = Image::make($url);

        // Rotate the image (you can specify the rotation angle)
        $rotatedImage = $image->rotate(90);
        $rotatedImage->save($url);
    }

    private static function convertImageminius90($file_name)
    {

        $url = Storage::disk('public')->path($file_name);

        // Load the image using Intervention/Image package
        $image = Image::make($url);

        // Rotate the image (you can specify the rotation angle)
        $rotatedImage = $image->rotate(-90);
        $rotatedImage->save($url);
    }

    public static function getLabelApi2(&$data): array
    {
        $url_get_label = env('URL_API_LABEL2');

        // get account number
        $setting_account = DB::table('settings_account_number')->orderBy('order_no')->get();
        $list_account = DB::table('account_number')->orderBy('order_no')->get();
        $limit_account = $setting_account[0]->value_;
        $index_account = $setting_account[1]->value_;

        $account = null;
        $count_while = $index_account;
        while ($account == null) {
            $sql = "SELECT account_number.account_no
                    FROM account_number
                    left join account_used on account_used.account_no = account_number.account_no
                    where account_number.order_no = :index";
//                    AND IFNULL(account_used.used_count,0) <= :limit";
            $data_account = DB::selectOne($sql, [
                'index' => $index_account
            ]);

            if ($data_account) {
                $account = $data_account->account_no;
            }
            $index_account += 1;
            if ($index_account > count($list_account)) {
                $index_account = 0;
            }
            if ($count_while > count($list_account) * 2) {
                break;
            }
            $count_while += 1;
        }
        if ($index_account >= count($list_account)) {
            $index_account = 0;
        }
        $response = Http::post($url_get_label . "/create", [
            'pkgInfo' => [
                'serviceName' => $data['service_name'],
                'countryCode' => 'US',
                'Signature' => $data['package']['signature'],
                'carbon' => false,
                'demoFlag' => false,
                'reference1' => $data['package']['ref1'] ?? '',
                'reference2' => $data['package']['ref2'] ?? '',
                'weight' => $data['package']['weight'],
                'length' => $data['package']['length'],
                'width' => $data['package']['width'],
                'height' => $data['package']['height'],
                'barcoderef' => false
            ],
            'fromCustomer' => [
                'address1' => $data['dataFrom']['address1'],
                'address2' => $data['dataFrom']['address2'] ?? '',
                'address3' => '',
                'city' => $data['dataFrom']['city'],
                'state' => $data['dataFrom']['state'],
                'name' => $data['dataFrom']['name'],
                'email' => '',
                'postalCode' => $data['dataFrom']['zip'],
                'companyName' => $data['dataFrom']['companyName'] ?? '',
                'phone' => $data['dataFrom']['phone'] ?? '',
                'fax' => '',
                'country' => 'United States',
                'countryName' => 'US',
            ],
            'toCustomer' => [
                'companyName' => $data['dataTo']['companyName'] ?? '',
                'name' => $data['dataTo']['name'],
                'address1' => $data['dataTo']['address1'],
                'address2' => $data['dataTo']['address2'] ?? '',
                'address3' => '',
                'city' => $data['dataTo']['city'],
                'state' => $data['dataTo']['state'],
                'email' => '',
                'fax' => '',
                'phone' => $data['dataTo']['phone'] ?? '',
                'postalCode' => $data['dataTo']['zip'],
                'countryCode' => 'United States',
                'res' => false
            ],
            'accountNumber' => 'real',
            'satDelivery' => $data['package']['saturdayDelivery'],
            'account_no' => $account
        ]);

        $dataBody = json_decode($response->body());
        $labelBase64 = $dataBody->label;
        $file_name = '/orders/' . $data['user_id'] . '/' . uniqid() . '.png';
        Storage::disk('public')->put($file_name, base64_decode($labelBase64));

        self::convertImage($file_name);

        // convert image to pdf
        $format_date = date_format(new DateTime(('now')), 'Ymdhms');
        $pdf_file_name = '/orders/' . $data['user_id'] . '/' . uniqid() . '_' . $format_date . '.pdf';

        $data['routeCode'] = $dataBody->routeCode;
        $data['lblservicename'] = $dataBody->lblservicename;
        $data['label_image'] = $file_name;
        $data['tracking_no'] = $dataBody->tracking;

        // PdfController::createPDFV2('storage' . $file_name, Storage::disk('public')->path($pdf_file_name));
        PdfController::genPDFCrushippers($data, Storage::disk('public')->path($pdf_file_name));

        Storage::disk('public')->delete($file_name);

        // update index account
        DB::table('settings_account_number')->where('key_', '=', 'index_account')
            ->update([
                'value_' => $index_account,
            ]);
        // update used account
        $account_used = DB::table('account_used')->where('account_no', '=', $account)->first();
        if ($account_used) {
            DB::table('account_used')->where('account_no', '=', $account)->update([
                'used_count' => $account_used->used_count + 1
            ]);
        } else {
            DB::table('account_used')->insert([
                'account_no' => $account,
                'used_count' => DB::raw('IFNULL(used_count,0) + 1')
            ]);
        }

        // update user limit
        DB::table('user_limit')->where('user_id', '=', $data['user_id'])->delete();
        DB::table('user_limit')->insert(['user_id' => $data['user_id']]);

        return [
            'tracking_no' => $dataBody->tracking,
            'file_name' => $pdf_file_name
        ];
    }

    public static function getLabelPython(&$data, $service_info): array
    {
        $url_get_label = env('URL_API_PYTHON');

        $dataSubmit = [
            'pkgInfo' => [
                'serviceName' => $service_info->service_data_name,
                'ref1' => $data['package']['ref1'] ?? '',
                'ref2' => '',
                'weight' => $data['package']['weight'],
                'length' => $data['package']['length'],
                'width' => $data['package']['width'],
                'height' => $data['package']['height'],
            ],
            'fromCustomer' => [
                'address1' => $data['dataFrom']['address1'],
                'address2' => $data['dataFrom']['address2'] ?? '',
                'city' => $data['dataFrom']['city'],
                'state' => $data['dataFrom']['state'],
                'name' => $data['dataFrom']['name'],
                'postalCode' => $data['dataFrom']['zip'],
                'companyName' => $data['dataFrom']['companyName'] ?? '',
                'phone' => $data['dataFrom']['phone'] ?? '',
            ],
            'toCustomer' => [
                'companyName' => $data['dataTo']['companyName'] ?? '',
                'name' => $data['dataTo']['name'],
                'address1' => $data['dataTo']['address1'],
                'address2' => $data['dataTo']['address2'] ?? '',
                'city' => $data['dataTo']['city'],
                'state' => $data['dataTo']['state'],
                'phone' => $data['dataTo']['phone'] ?? '',
                'postalCode' => $data['dataTo']['zip'],
            ],
            'insurance' => true,
            'saturdayDelivery' => $data['package']['saturdayDelivery'] == 'on',
            'declaredValue' => '100',
            'deliveryConfirmation' => true,
            'specialInstructions' => "Handle with care",
            'residentialDelivery' => true
        ];
        $response = Http::post($url_get_label . "/create", $dataSubmit);

        $dataBody = json_decode($response->body());
        $labelBase64 = $dataBody->shippingLabelImage;
        $file_name = '/orders/' . $data['user_id'] . '/' . uniqid() . '.png';
        Storage::disk('public')->put($file_name, base64_decode($labelBase64));

        self::convertImageminius90($file_name);

        // convert image to pdf
        $format_date = date_format(new DateTime(('now')), 'Ymdhms');
        $pdf_file_name = '/orders/' . $data['user_id'] . '/' . uniqid() . '_' . $format_date . '.pdf';

        PdfController::createPDFV3('storage' . $file_name, Storage::disk('public')->path($pdf_file_name));

        Storage::disk('public')->delete($file_name);
        return [
            'tracking_no' => $dataBody->trackingNumber,
            'file_name' => $pdf_file_name
        ];
    }

    public static function getLabelPirreas($data, $service_info): array
    {
        try {
            $url_get_label = env('URL_API_LABEL_PIRREAS');
            $api_key = env('API_KEY_PIRREAS');

            $response = Http::withHeaders([
                'x-api-token' => $api_key,
            ])->post($url_get_label, [
                'serviceId' => $service_info->serviceIdMap,
                'sender' => [
                    'firstName' => $data['dataFrom']['name'],
                    'lastName' => $data['dataFrom']['companyName'] ?? '',
                    'postcode' => $data['dataFrom']['zip'],
                    'street' => $data['dataFrom']['address1'],
                    'street2' => $data['dataFrom']['address2'] ?? '',
                    'phone' => $data['dataFrom']['phone'] ?? '',
                    'city' => $data['dataFrom']['city'],
                    'state' => $data['dataFrom']['state'],
                    'countryCode' => 'US',
                ],
                'receiver' => [
                    'firstName' => $data['dataTo']['name'],
                    'lastName' => $data['dataTo']['companyName'] ?? '',
                    'postcode' => $data['dataTo']['zip'],
                    'street' => $data['dataTo']['address1'],
                    'street2' => $data['dataTo']['address2'] ?? '',
                    'phone' => $data['dataTo']['phone'] ?? '',
                    'city' => $data['dataTo']['city'],
                    'state' => $data['dataTo']['state'],
                    'countryCode' => 'US',
                ],
                'weight' => $data['package']['weight'],
                'height' => $data['package']['height'],
                'length' => $data['package']['length'],
                'width' => $data['package']['width'],
            ]);
            $tracking_no = $response->header('tracking-no');
            $format_date = date_format(new DateTime(('now')), 'Ymdhms');
            $file_name = '/orders/' . $data['user_id'] . '/' . uniqid() . '_' . $format_date . '.pdf';
            Storage::disk('public')->put($file_name, $response->body());
            return [
                'tracking_no' => $tracking_no,
                'file_name' => $file_name
            ];
        } catch (Exception $e) {
            return [
                'tracking_no' => '',
                'file_name' => null
            ];
        }
    }

    public function getListOrderAdmin(Request $request): JsonResponse
    {
        $query_params = $request->all();

        $query = DB::table('orders')
            ->leftJoin('services', 'services.id', '=', 'orders.service_id')
            ->leftJoin('users', 'users.id', '=', 'orders.user_id');


        if ($query_params['status'] != 0) {
            $query->where('orders.status', '=', $query_params['status']);
        }

        if (isset($query_params['user_id'])) {
            $query->whereIn('orders.user_id', explode(",", $query_params['user_id']));
        }

        if ($query_params['status'] != 0) {
            $query->where('orders.status', '=', $query_params['status']);
        }

        if (isset($query_params['service']) && $query_params['service']  == 'ontrac') {
            $query->where('orders.service_id', '=', 0);
        } else {
            $query->where('orders.service_id', '<>', 0);
        }

        if (isset($query_params['dateRange'])) {
            $arrDate = explode(" ", $query_params['dateRange']);
            $dateFrom = $arrDate[0] ?? new DateTime('now');
            $dateTo = $arrDate[2] ?? $dateFrom;
            $query->whereBetween('orders.date_create', [$dateFrom, $dateTo]);
        }

        if (isset($query_params['q'])) {
            $query->where(function ($q) use ($query_params) {
                $q->where("from_name", 'like', '%' . $query_params["q"] . '%')
                    ->orWhere("to_name", 'like', '%' . $query_params["q"] . '%')
                    ->orWhere("tracking_id", 'like', '%' . $query_params["q"] . '%');
            });
        }

        if (isset($query_params['service_id'])) {
            $query->whereIn('service_id', explode(",", $query_params['service_id']));
        }

        if (isset($query_params['tracking_status'])) {
            $query->whereIn('tracking_status', explode(",", $query_params['tracking_status']));
        }

        if ($query_params['orderBy'] == 1) {
            $query->orderBy('orders.create_at');
        } else {
            $query->orderByDesc('orders.create_at');
        }

        $orders = $query->select(
            'orders.*',
            'services.service_name',
            'users.name as username'
        )->paginate($query_params['itemsPerPage'] ?? 10);
        return response()->json([
            'status' => true,
            'orders' => $orders
        ]);
    }

    public function getListOrder(Request $request): JsonResponse
    {
        $query_params = $request->all();
        $query = DB::table('orders')
            ->leftJoin('services', 'services.id', '=', 'orders.service_id');

        $query->where('orders.user_id', '=', $request->user()->id);
        if ($query_params['status'] != 0) {
            $query->where('orders.status', '=', $query_params['status']);
        }

        if (isset($query_params['service']) && $query_params['service']  == 'ontrac') {
            $query->where('orders.service_id', '=', 0);
        } else {
            $query->where('orders.service_id', '<>', 0);
        }

        if (isset($query_params['dateRange'])) {
            $arrDate = explode(" ", $query_params['dateRange']);
            $dateFrom = $arrDate[0] ?? new DateTime('now');
            $dateTo = $arrDate[2] ?? $dateFrom;
            $query->whereBetween('orders.date_create', [$dateFrom, $dateTo]);
        }

        if (isset($query_params['q'])) {
            $query->where(function ($q) use ($query_params) {
                $q->where("from_name", 'like', '%' . $query_params["q"] . '%')
                    ->orWhere("to_name", 'like', '%' . $query_params["q"] . '%')
                    ->orWhere("tracking_id", 'like', '%' . $query_params["q"] . '%');
            });
        }

        if (isset($query_params['service_id'])) {
            $query->whereIn('service_id', explode(",", $query_params['service_id']));
        }

        if (isset($query_params['tracking_status'])) {
            $query->whereIn('tracking_status', explode(",", $query_params['tracking_status']));
        }

        if ($query_params['orderBy'] == 1) {
            $query->orderBy('orders.create_at');
        } else {
            $query->orderByDesc('orders.create_at');
        }

        $orders = $query->select(
            'orders.*',
            'services.service_name',
        )->paginate($query_params['itemsPerPage'] ?? 10);
        return response()->json([
            'status' => true,
            'orders' => $orders
        ]);
    }

    public function getListServiceSearch(): JsonResponse
    {
        return response()->json([
            'status' => true,
            'service' => DB::table('services')->where('status', '=', 1)
                ->where('is_deleted', '=', 0)->get()
        ]);
    }

    public function getListTransaction(Request $request): JsonResponse
    {
        $query_param = $request->all();
        $query = DB::table('transaction');
        $query_param['user_id'] = $request->user()->id;
        $query->where('user_id', '=', $query_param['user_id']);

        if (isset($query_param['dateRange'])) {
            $arrDate = explode(" ", $query_param['dateRange']);
            $dateFrom = $arrDate[0] ?? new DateTime('now');
            $dateTo = $arrDate[2] ?? $dateFrom;
            $query->whereBetween('date_create', [$dateFrom, $dateTo]);
        }

        if (isset($query_param['transactionType'])) {
            $query->where('type', '=', $query_param['transactionType']);
        }

        if (isset($query_param['q'])) {
            $query->where(function ($q) use ($query_param) {
                $q->where("id", '=', $query_param["q"])
                    ->orWhere("note", 'like', '%' . $query_param["q"] . '%');
            });
        }

        $query->orderBy('create_at', 'desc');

        $data_transaction = $query->paginate($query_param['itemsPerPage'] ?? 10);
        return response()->json([
            'transaction' => $data_transaction,
            'message' => ''
        ]);
    }

    /**
     * getListPackage
     * @param Request $request
     * @return JsonResponse
     */
    public function getListPackage(Request $request): JsonResponse
    {
        $query_param = $request->all();
        $query = DB::table('package_save');

        $userID = $request->user()->id;

        if (isset($query_param['q'])) {
            $query->where('package_name', 'like', '%' . $query_param["q"] . '%');
        }
        $query->where('user_id', '=', $userID);
        $query->orderBy('create_at', 'desc');

        $data_package = $query->paginate($query_param['itemsPerPage'] ?? 10);
        return response()->json([
            'package_saved' => $data_package,
            'message' => ''
        ]);
    }

    /**
     * deletePackage
     * @param $id
     * @return JsonResponse
     */
    public function deletePackage($id): JsonResponse
    {
        DB::table('package_save')->where('id', '=', $id)->delete();
        return response()->json([
            'status' => true
        ]);
    }

    /**
     * updatePackage
     * @param Request $request
     * @return JsonResponse
     * @throws Exception
     */
    public function updatePackage(Request $request): JsonResponse
    {
        $data = $request->all();
        $userID = $request->user()->id;
        $dataUpdate = [
            'user_id' => $userID,
            'package_name' => $data['package_name'],
            'no_of_package' => $data['no_of_package'],
            'weight' => $data['weight'],
            'length' => $data['length'],
            'width' => $data['width'],
            'height' => $data['height'],
            'ref1' => $data['ref1'],
            'ref2' => $data['ref2'],
            'create_at' => new DateTime(('now'))
        ];
        if (isset($data['id'])) {
            DB::table('package_save')->where('id', '=', $data['id'])
                ->update($dataUpdate);
        } else {
            DB::table('package_save')->insert($dataUpdate);
        }

        return response()->json([
            'status' => true
        ]);
    }

    public function getDataPrice(Request $request): JsonResponse
    {
        $data = [];
        $query_params = $request->all();
        $query = DB::table('services')
            ->join('carrier', 'carrier.id', '=', 'services.carrier_id')
            ->join('user_service', 'user_service.service_id', '=', 'services.id')
            ->where('services.is_deleted', '<>', 1)
            ->where('services.status', '=', 1)
            ->where('carrier.status', '=', 1)
            ->where('user_service.user_id', '=', $query_params['user_id']);

        if (isset($query_params['status'])) {
            $query = $query->where('user_service.is_enable', '=', $query_params['status']);
        }
        $query = $query->select([
            'carrier.id as carrier_id',
            'carrier_name',
            'services.id as service_id',
            'carrier.logo',
            'service_name',
            'services.max_weight',
            'user_service.is_enable'
        ]);
        $dataService = $query->get();

        foreach ($dataService as $item) {
            $price = DB::table('custom_price')
                ->where('service_id', '=', $item->service_id)
                ->where('user_id', '=', $query_params['user_id'])
                ->get();
            if (count($price) > 0) {
                $data[] = [
                    'service' => $item,
                    'item' => $price
                ];
            }
        }
        return response()->json([
            'price' => $data,
            'message' => ''
        ]);
    }

    public function getOrderDetail($order_id, Request $request): JsonResponse
    {
        $dataOrder = DB::table('orders')
            ->leftJoin('services', 'services.id', '=', 'orders.service_id')
            ->leftJoin('carrier', 'carrier.id', '=', 'services.carrier_id')
            ->where('orders.id', '=', $order_id)
            ->select([
                'orders.*',
                'services.service_name',
                'carrier.carrier_name',
                'carrier.logo'
            ])->first();
        return response()->json([
            'status' => 200,
            'order' => $dataOrder
        ]);
    }

    public function duplicateLabel(Request $request): JsonResponse
    {
        $data = $request->all();
        $dataOrder = DB::table('orders')
            ->join('services', 'services.id', '=', 'orders.service_id')
            ->where('orders.id', '=', $data['order_id'])
            ->select([
                'orders.*',
                'services.service_name'
            ])
            ->first();
        if (!$dataOrder) {
            return response()->json([
                'status' => 422,
                'message' => 'Order not found'
            ]);
        }

        $service_info = DB::table('services')->where('id', '=', $dataOrder->service_id)->first();
        if (!$service_info) {
            return response()->json([
                'status' => 422,
                'message' => 'Service not found'
            ]);
        }

        $user_id = $request->user()->id;
        // check balance
        $userInfo = DB::table('users')->where('id', '=', $user_id)->first();
        if (!isset($userInfo)) {
            return response()->json([
                'status' => 422,
                'message' => 'User does not exist'
            ]);
        }
        $current_balance = $userInfo->balance;

        $price_unit = $dataOrder->price - ($dataOrder->price * (($dataOrder->discount ?? 0) / 100));

        if ($price_unit > $current_balance) {
            return response()->json([
                'status' => 422,
                'message' => 'Not enough money'
            ]);
        }

        $new_date = new DateTime(('now'));
        // create order
        $order_id = uniqid() . '_' . date_format($new_date, 'YMdhms');

        DB::table('orders')->insert([
            'id' => $order_id,
            'date_create' => $new_date,
            'from_name' => $dataOrder->from_name,
            'to_name' => $dataOrder->to_name,
            'no_of_package' => $dataOrder->no_of_package,
            'weight' => $dataOrder->weight,
            'length' => $dataOrder->length,
            'width' => $dataOrder->width,
            'height' => $dataOrder->height,
            'ref1' => $dataOrder->ref1,
            'ref2' => $dataOrder->ref2,
            'saturday_delivery' => $dataOrder->saturday_delivery ? 1 : 0,
            'signature' => $dataOrder->signature ? 1 : 0,
            'service_id' => $dataOrder->service_id,
            'to_company' => $dataOrder->to_company,
            'to_phone' => $dataOrder->to_phone,
            'to_country' => $dataOrder->to_country,
            'to_address1' => $dataOrder->to_address1,
            'to_address2' => $dataOrder->to_address2,
            'to_zip' => $dataOrder->to_zip,
            'to_city' => $dataOrder->to_city,
            'to_state' => $dataOrder->to_state,
            'from_company' => $dataOrder->from_company,
            'from_country' => $dataOrder->from_country,
            'from_phone' => $dataOrder->from_phone,
            'from_address1' => $dataOrder->from_address1,
            'from_address2' => $dataOrder->from_address2,
            'from_zip' => $dataOrder->from_zip,
            'from_city' => $dataOrder->from_city,
            'from_state' => $dataOrder->from_state,
            'status' => self::STATUS_ORDER_CANCEL,
            'price' => $dataOrder->price,
            'user_id' => $dataOrder->user_id,
            'tracking_status' => 'Pre_transit',
            'create_at' => $new_date,
            'delivery_date' => $dataOrder->delivery_date,
            'discount' => $dataOrder->discount,
            'total_price' => $dataOrder->total_price,
        ]);

        $dataBookLabel = [
            "service_name" => $dataOrder->service_name,
            "package" => [
                "signature" => $dataOrder->signature === 1,
                'ref1' => $dataOrder->ref1 ?? '',
                'ref2' => $dataOrder->ref2 ?? '',
                'weight' => $dataOrder->weight,
                'length' => $dataOrder->length,
                'width' => $dataOrder->width,
                'height' => $dataOrder->height,
                'saturdayDelivery' => $dataOrder->saturday_delivery === 1,
            ],
            'dataFrom' => [
                'address1' => $dataOrder->from_address1,
                'address2' => $dataOrder->from_address2 ?? '',
                'city' => $dataOrder->from_city,
                'state' => $dataOrder->from_state,
                'name' => $dataOrder->from_name,
                'email' => '',
                'zip' => $dataOrder->from_zip,
                'companyName' => $dataOrder->from_company ?? '',
                'phone' => $dataOrder->from_phone ?? '',
                'fax' => '',
                'country' => 'United States',
                'countryName' => 'US',
            ],
            'dataTo' => [
                'companyName' => $dataOrder->to_company ?? '',
                'name' => $dataOrder->to_name,
                'address1' => $dataOrder->to_address1,
                'address2' => $dataOrder->to_address2 ?? '',
                'city' => $dataOrder->to_zip,
                'state' => $dataOrder->to_state,
                'email' => '',
                'fax' => '',
                'phone' => $dataOrder->to_phone ?? '',
                'zip' => $dataOrder->to_zip,
                'countryCode' => 'United States',
                'res' => false
            ],
            'user_id' => $user_id,
        ];

        if ($service_info->provider == 'pirreas.com') {
            $rs = $this->getLabelPirreas($dataBookLabel, $service_info);
        } else if ($service_info->provider == 'api_python') {
            $rs = $this->getLabelPython($dataBookLabel, $service_info);
        } else if ($service_info->provider == 'bulkzsp') {
            $rs = $this->uspsService->getLabel($dataBookLabel, $service_info);
        } else if ($service_info->provider == 'usps_template') {
            $rs = $this->uspsService->createLabel($dataBookLabel, $service_info);
        } else {
            $rs = $this->getLabelApi2($dataBookLabel);
        }

        if ($rs['tracking_no'] == '') {
            return response()->json([
                'status' => 422,
                'message' => 'Cannot create label. Please try again'
            ]);
        }

        // update current balance
        $after_balance = $current_balance - $price_unit;
        DB::table('users')
            ->where('id', '=', $user_id)
            ->update(['balance' => $after_balance]);

        DB::table('transaction')->insert([
            'id' => uniqid('TRANS_') . date_format($new_date, 'YMdhms'),
            'type' => self::STATUS_TRANSACTION_ORDER,
            'user_id' => $user_id,
            'amount' => -$price_unit,
            'current_amount' => $after_balance,
            'note' => 'Order created successfully.' . PHP_EOL . ' Order ID: ' . $order_id,
            'create_at' => $new_date,
            'date_create' => $new_date
        ]);

        // update order
        DB::table('orders')->where('id', '=', $order_id)
            ->update([
                'status' => self::STATUS_ORDER_DONE,
                'tracking_id' => $rs['tracking_no'],
                'label_image' => $rs['file_name']
            ]);
        return response()->json([
            'status' => 200,
            'current_balance' => $current_balance,
            'dataLabel' => [
                'label_image' => $rs['file_name'],
                'from_name' => $dataOrder->from_name,
                'to_name' => $dataOrder->to_name
            ]
        ]);
    }

    public function addRefund(Request $request): JsonResponse
    {
        $data = $request->all();
        $user = $request->user();
        $dataOrder = DB::table('orders')->where('id', '=', $data['order_id'])
            ->first();
        if (!$dataOrder) {
            return response()->json([
                'status' => 422,
                'message' => 'User does not exist'
            ]);
        }
        DB::transaction(function () use ($dataOrder, $user, $data) {
            DB::table('order_refund')->insert([
                'order_id' => $dataOrder->id,
                'user_id' => $user->id,
                'tracking_id' => $dataOrder->tracking_id,
                'price' => $dataOrder->price,
                'balance' => $user->balance,
                'tracking_status' => $dataOrder->tracking_status,
                'status' => self::STATUS_REFUND_PENDING,
                'note' => $data['reason'] ?? 'User add refund',
                'order_date' => $dataOrder->date_create,
                'order_date_time' => $dataOrder->create_at,
                'create_at' => new DateTime('now'),
            ]);

            DB::table('orders')->where('id', '=', $dataOrder->id)
                ->update([
                    'status' => self::STATUS_ORDER_REFUND
                ]);
        });

        return response()->json([
            'status' => 200
        ]);
    }

    public function getRefund(Request $request): JsonResponse
    {
        $query_params = $request->all();
        $query = DB::table('order_refund')
            ->join('users', 'users.id', '=', 'order_refund.user_id');

        if (isset($query_params['dateRange'])) {
            $arrDate = explode(" ", $query_params['dateRange']);
            $dateFrom = $arrDate[0] ?? new DateTime('now');
            $dateTo = $arrDate[2] ?? $dateFrom;
            $query->whereBetween('order_refund.order_date', [$dateFrom, $dateTo]);
        }
        if (isset($query_params['users'])) {
            $query->whereIn('order_refund.user_id', explode(",", $query_params['users']));
        }

        if (isset($query_params['status'])) {
            $query->where('order_refund.status', '=', $query_params['status']);
        }

        if (isset($query_params['sortby']) && $query_params['sortby'] == 2) {
            $query->orderByDesc('order_refund.create_at');
        } else {
            $query->orderBy('order_refund.create_at');
        }

        $orders_refund = $query->select(
            'order_refund.*',
            'users.name as user_name',
        )->paginate($query_params['itemsPerPage'] ?? 10);
        return response()->json([
            'status' => true,
            'orders_refund' => $orders_refund
        ]);
    }

    public function updateStatusRefund(Request $request): JsonResponse
    {
        $data = $request->all();

        DB::transaction(function () use ($data) {
            DB::table('order_refund')->where('id', '=', $data['id'])
                ->update([
                    'status' => $data['status']
                ]);

            if ($data['status'] == self::STATUS_REFUND_REJECTED) {
                DB::table('orders')->where('id', '=', $data['order_id'])
                    ->update([
                        'status' => self::STATUS_ORDER_DONE
                    ]);
            } else if ($data['status'] == self::STATUS_REFUND_ACCEPTED) {
                $user = DB::table('users')
                    ->where('id', '=', $data['user_id'])
                    ->first();

                if ($user) {
                    $current_balance = $user->balance + $data['price'];

                    DB::table('users')
                        ->where('id', '=', $data['user_id'])
                        ->update(['balance' => $current_balance]);
                    $new_date = new DateTime(('now'));
                    DB::table('transaction')->insert([
                        'id' => uniqid('TRANS_') . date_format($new_date, 'YMdhms'),
                        'type' => self::STATUS_TRANSACTION_REFUND,
                        'user_id' => $data['user_id'],
                        'amount' => $data['price'],
                        'current_amount' => $current_balance,
                        'note' => 'Refund Order ID: ' . $data['order_id'],
                        'create_at' => new DateTime('now'),
                        'date_create' => new DateTime('now'),
                    ]);

                }
            }
        });
        return response()->json([
            'status' => true,
        ]);
    }

    public function sendMessage($message): void
    {
        try {
            $bot = $this->telegramService->getBot('bookLabelBot');
            $bot->sendMessage([
                'chat_id' => env('TELEGRAM_GROUP_ID_BOOK_LABEL', 'TELEGRAM_GROUP_ID_BOOK_LABEL'),
                'text' => $message,
                'parse_mode' => 'HTML',
            ]);
        } catch (Exception $ex) {

        }
    }

    public function callbackUrl(Request $request)
    {
        $data = $request->all();
        $this->sendMessage(json_encode($data));
        return response()->json([
            'status' => true,
        ]);
    }
}
