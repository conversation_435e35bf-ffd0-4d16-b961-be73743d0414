# Script to start multiple Laravel queue workers in parallel

# Set path to your project directory
$projectPath = "C:\api\starter-kit-api"

# Set path to PHP executable
$phpPath = "C:\xampp\php\php.exe"

# Number of workers you want to start
$numberOfWorkers = 1

# Start multiple workers in parallel
for ($i = 1; $i -le $numberOfWorkers; $i++) {
    Start-Process -FilePath $phpPath -ArgumentList "$projectPath\artisan queue:work database --queue=default --sleep=3 --tries=3" -NoNewWindow
}

Write-Output "Laravel queue workers started."
exit