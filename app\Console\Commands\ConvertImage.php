<?php

namespace App\Console\Commands;

use DateTime;
use Dompdf\Dompdf;
use Dompdf\Options;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\View;

class ConvertImage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:convert-image';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $data = DB::table('orders')->where("label_image", 'like', '%.png')->orderByDesc('create_at')->get();
		
		$total_data = count($data);
		
	    $this->info('Run total: '. $total_data);
        foreach ($data as $key => $item) {
            $file_image = public_path('storage'. $item->label_image);
            if (!File::exists($file_image)) {
	            $this->info('NG '. $key .' / '. $total_data . ': ' . $file_image. ' not exists');
                continue;
            }

            $options = new Options();
            $options->set('isHtml5ParserEnabled', true);
            $options->set('isRemoteEnabled', true);
            $options->set('margin_top', 0);
            $options->set('margin_bottom', 0);
            $options->set('margin_left', 0);
            $options->set('margin_right', 0);
            $options->setIsRemoteEnabled(true);

            $dompdf = new Dompdf($options);

            $dompdf->set_option('tempDir', storage_path('app/tmp'));

            $dompdf->setPaper(array(0, 0, 270, 405), 'portrait');

            $data = ['image_url' => 'https://api.crushippers.com/storage' . $item->label_image];

            // HTML content for the PDF
            $view = View::make('template_pdf.order3', $data)->render();

            // Load HTML content into Dompdf
            $dompdf->loadHtml($view);

            // Output PDF as a file (optional)
            // Render PDF (output)
            $dompdf->render();

            $format_date = date_format(new DateTime(('now')), 'Ymdhms');
            $pdf_file = '/orders/' . $item->user_id . '/' . uniqid() . '_' . $format_date . '.pdf';
	        
	        $this->info('OK '. $key .' / '. $total_data . ': ' . $file_image. ' success');
            // Save PDF to the server's filesystem
	        file_put_contents(Storage::disk('public')->path($pdf_file), $dompdf->output());

            DB::table('orders')->where('id', '=', $item->id)->update([
                'label_image' => $pdf_file,
            ]);
            Storage::disk('public')->delete($item->label_image);
        }
    }
}
