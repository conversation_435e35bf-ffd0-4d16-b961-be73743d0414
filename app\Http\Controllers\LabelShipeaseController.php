<?php

namespace App\Http\Controllers;

use DateTime;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class LabelShipeaseController extends Controller
{
	public static function getLabelShipease(Request $request)
	{
		$url_get_label = env('URL_API_LABEL2_SHIPEASE');

		$data = $request->all();

		$data['user_id'] = 'test';

		$settings_account_number = DB::connection('mysql_shipease')->table('settings_shipease')->orderBy('order_no')->get();
		$list_account = DB::connection('mysql_shipease')->table('account_number_shipease')->orderBy('order_no')->get();
		$index_account = $settings_account_number[0]->value_;
		$limit_time = $settings_account_number[1]->value_;

		$sql_checkExecute = "select * from user_limit WHERE user_name = :user_id
		and NOW() >= DATE_ADD(create_date,INTERVAL :limit_time SECOND)";

		$user_limit = DB::connection('mysql_shipease')->select($sql_checkExecute, [
			'user_id' => $data['user_id'],
			'limit_time' => $limit_time
		]);

		if (count($user_limit) <= 0) {
			return response()->json([
				'error' => true,
				'message' => 'Cannot create label. Please try again ' . $limit_time . '(s)'
			]);
		}

		$account = null;
		$count_while = $index_account;
		while ($account == null) {
			$sql = "SELECT account_number.account_no
                    FROM account_number_shipease account_number
                    where account_number.order_no = :index";
			$data_account = DB::connection('mysql_shipease')->selectOne($sql, [
				'index' => $index_account
			]);

			if ($data_account) {
				$account = $data_account->account_no;
			}
			$index_account += 1;
			if ($index_account > count($list_account)) {
				$index_account = 0;
			}
			if ($count_while > count($list_account) * 2) {
				break;
			}
			$count_while += 1;
		}
		if ($index_account >= count($list_account)) {
			$index_account = 0;
		}

		$data['account_no'] = $account;

		$response = Http::post($url_get_label . "/create", $data);

		$dataBody = json_decode($response->body());
		$labelBase64 = $dataBody->label;
		$file_name = '/orders/' . $data['user_id'] . '/' . uniqid() . '.png';
		Storage::disk('public')->put($file_name, base64_decode($labelBase64));

		LabelController::convertImage($file_name);

		// convert image to pdf
		$format_date = date_format(new DateTime(('now')), 'Ymdhms');
		$pdf_file_name = '/orders/' . $data['user_id'] . '/' . uniqid() . '_' . $format_date . '.pdf';

		$data['routeCode'] = $dataBody->routeCode;
		$data['lblservicename'] = $dataBody->lblservicename;
		$data['label_image'] = $file_name;
		$data['tracking_no'] = $dataBody->tracking;
		$data['pkgInfo']['saturdayDelivery'] = $data['satDelivery'];

		PdfController::genPDFLabelApi2Shipease($data, Storage::disk('public')->path($pdf_file_name));

		Storage::disk('public')->delete($file_name);

		// update index account
		DB::connection('mysql_shipease')->table('settings_shipease')->where('key_', '=', 'index_account')
			->update([
				'value_' => $index_account,
			]);

        DB::connection('mysql_shipease')->table('user_limit')->where('user_name', '=', $data['user_id'])
            ->delete();

        DB::connection('mysql_shipease')->table('user_limit')
            ->insert([
                'user_name' => $data['user_id'],
            ]);

		DB::connection('mysql_shipease')->table('history_label_shipease')
			->insert([
				'from' => $data['fromCustomer']['name'],
				'to' => $data['toCustomer']['name'],
				'service' => $data['pkgInfo']['serviceName'],
				'tracking' => $dataBody->tracking,
				'create_at' => new DateTime(),
				'url_label' => $pdf_file_name
			]);

		$dataAddressBook = [];

		if ($data["saveAddressFrom"]) {
			$dataAddressBook[] = [
				"country" => $data['fromCustomer']["country"],
				"name" => $data['fromCustomer']["name"],
				"company_name" => $data['fromCustomer']["companyName"],
				"address1" => $data['fromCustomer']["address1"],
				"address2" => $data['fromCustomer']["address2"],
				"city" => $data['fromCustomer']["city"],
				"state" => $data['fromCustomer']["state"],
				"zipcode" => $data['fromCustomer']["postalCode"],
				"phone" => $data['fromCustomer']["phone"],
			];
		}

		if ($data["saveAddressTo"]) {
			$dataAddressBook[] = [
				"country" => $data['toCustomer']["countryCode"],
				"name" => $data['toCustomer']["name"],
				"company_name" => $data['toCustomer']["companyName"],
				"address1" => $data['toCustomer']["address1"],
				"address2" => $data['toCustomer']["address2"],
				"city" => $data['toCustomer']["city"],
				"state" => $data['toCustomer']["state"],
				"zipcode" => $data['toCustomer']["postalCode"],
				"phone" => $data['toCustomer']["phone"],
			];
		}
		DB::connection('mysql_shipease')->table('address_book')->insert($dataAddressBook);
		return [
			'tracking_no' => $dataBody->tracking,
			'file_name' => $pdf_file_name
		];
	}

	public function getOrder(Request $request): JsonResponse
	{
		$query = DB::connection('mysql_shipease')->table('history_label_shipease')->orderByDesc('create_at');
		if ($request->query('search')) {
			$keySearch = $request->query('search');
			$query->where("to", 'like', '%' . $keySearch . '%')
				->orWhere("from", 'like', '%' . $keySearch . '%')
				->orWhere("tracking", 'like', '%' . $keySearch . '%');
		}
		$data = $query->paginate($request->query('limit') ?? 10);
		return response()->json($data);
	}
}
