<?php

namespace App\Http\Controllers;

use DateTime;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TicketController extends Controller
{
    public function getListTicketForUser(Request $request): JsonResponse
    {
	    $query_param = $request->all();
	    $query = DB::table('ticket')->where('user_id', '=', $request->user()->id);
	    if(isset($query_param['q'])) {
		    $query->where("subject", 'like', '%' . $query_param["q"] . '%');
	    }
	    if(isset($query_param['status'])) {
		    $query->where("status", '=', $query_param["status"]);
	    }
		if (isset($query_param['orderBy']) && $query_param['orderBy'] == 1) {
			$query->orderBy('update_at');
		} else {
			$query->orderByDesc('update_at');
		}
	    
	    $data_ticket = $query->paginate($query_param['itemsPerPage'] ?? 10);
	    return response()->json([
		    'ticket' => $data_ticket,
		    'message' => ''
	    ]);
    }
	
	public function getListTicketForAdmin(Request $request): JsonResponse
	{
		$query_param = $request->all();
		$query = DB::table('ticket')
			->join('users', 'users.id', '=', 'ticket.user_id');
		
		if(isset($query_param['q'])) {
			$query->where("subject", 'like', '%' . $query_param["q"] . '%');
		}
		
		if(isset($query_param['users'])) {
			$query->whereIn('user_id', explode(",", $query_param['users']));
		}
		
		if(isset($query_param['status'])) {
			$query->where("ticket.status", '=', $query_param["status"]);
		}
		if (isset($query_param['orderBy']) && $query_param['orderBy'] == 1) {
			$query->orderBy('ticket.update_at');
		} else {
			$query->orderByDesc('ticket.update_at');
		}
		
		$data_ticket = $query->select([
			'ticket.*',
			'users.name as username'
		])->paginate($query_param['itemsPerPage'] ?? 10);
		return response()->json([
			'ticket' => $data_ticket,
			'message' => ''
		]);
	}
	
	public function getTicketDetail(Request $request): JsonResponse
	{
		$data = $request->all();
		$dataTicket = DB::table('ticket_detail')
			->join('users', 'users.id', '=', 'ticket_detail.user_id')
			->where('ticket_id', $data['id'])
			->orderByDesc('create_at')
			->select([
				'users.name as username',
				'content',
				'ticket_detail.create_at'
			])->get();
		return response()->json([
			'ticket_detail' => $dataTicket,
			'message' => ''
		]);
	}
	
	public function updateStatus(Request $request): JsonResponse
	{
		$data = $request->all();
		DB::table('ticket')->where('id', '=', $data['id'])
			->update([
				'status' => $data['status'],
				'update_at' => new DateTime('now')
			]);
		return response()->json([
			'status' => true
		]);
	}
	
	public function createTicket(Request $request): JsonResponse
	{
		$data = $request->all();
		$userId = $request->user()->id;
		DB::transaction(function () use ($data, $userId) {
			$new_date = new DateTime('now');
			
			if (!isset($data['id'])) {
				$ticket_id = DB::table('ticket')->insertGetId([
					'user_id' => $userId,
					'subject' => $data['subject'],
					'status' => 0,
					'create_at' => $new_date,
					'update_at' => $new_date
				]);
			} else {
				$ticket_id = $data['id'];
				DB::table('ticket')->where('id', '=', $ticket_id)
				->update([
					'subject' => $data['subject'],
					'update_at' => $new_date
				]);
			}
			
			DB::table('ticket_detail')->insert([
				'ticket_id' => $ticket_id,
				'user_id' => $userId,
				'content' => $data['content'],
				'create_at' => $new_date
			]);
		});
		
		return response()->json([
			'status' => true
		]);
	}
}
