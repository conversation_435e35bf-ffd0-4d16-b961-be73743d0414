<?php

namespace App\Console\Commands;

use DateTime;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Telegram\Bot\Laravel\Facades\Telegram;

class Test extends Command
{
    private $process_id;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test {process_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $response = Http::get('https://api.crushippers.com/storage//ups_checker/66e5d8cd16888/list.txt');
        $pathFileData = $response->body();
        $file_name = '/ups_checker/' . uniqid() . '.txt';
        Storage::disk('public')->put($file_name, $pathFileData);
        $fileProcess = public_path('storage/' . $file_name);

        $lines = $this->readFileData($fileProcess);
        Storage::disk('public')->delete($file_name);
        $dataAccount = DB::table('work_account_process')
            ->where('status', '=', 'New')
            ->orWhere('is_failed', '=', 1)
            ->get();

        $this->process_id = $this->argument('process_id');

        DB::table('work_account_process')->where('process_id', $this->process_id)->update(['status' => 'Processing']);
        Log::channel('database')->info('Update Status Processing', [$this->process_id]);

        $dataProcess = DB::table('work_account_process')->where('process_id', [$this->process_id])->first();
        if (empty($dataProcess)) {
            Log::channel('database')->error('Data Process is empty', [$this->process_id]);
            return;
        }


        $num_account_processed = 0;
        $num_account_succeed = 0;
        $num_account_failed = 0;

        $index = 0;
        while ($index < count($lines)) {
            $account = $lines[$index];
            try {
                // check action
                $processAction = DB::table('work_account_process')->where('process_id', $this->process_id)->first();
                if ($processAction->status == 'stop') {
                    DB::table('work_account_process')->where('process_id', $this->process_id)
                        ->update([
                            'status' => 'Success',
                            'action' => 'stop',
                            'date_update' => new DateTime('now')
                        ]);
                    Log::channel('database')->info('Processing stop', [$this->process_id]);
                    return;
                }

                $num_account_processed += 1;
                $accountExists = DB::table('work_account_detail')
                    ->where('account_number', $account)
                    ->first();
                if (!empty($accountExists)) {
                    $num_account_failed += 1;
                    $index += 1;
                    Log::channel('database')->error('Account '. $account .' is exists', [$this->process_id]);
                    continue;
                }

                DB::table('work_account_detail')->where('account_number', $account)->delete();

                // get header
                $header = DB::table('header_ups')->where('headers_id', $dataProcess->header_id)->first();
                if (empty($header) || $header->status === 'Not Live') {
                    $num_account_failed += 1;
                    Log::channel('database')->error('Header is empty', [$this->process_id]);
                    continue;
                }
                $headerText = $header->headers;
                $headerLines = explode("\n", $headerText);
                $headers = [];
                foreach ($headerLines as $line) {
                    list($key, $value) = explode(':', $line, 2);
                    $headers[$key] = $value;
                }

                Log::channel('database')->info('Process Account: ' . $account, [$this->process_id]);
                $response = Http::retry(3, 500)->withHeaders($headers)->post('https://webapis.ups.com/doapp/api/Registration/ReadExistingAccount',
                    $account
                );
                if ($response->ok()) {
                    $dataBody = json_decode($response->body());
                    if (isset($dataBody->Object->postalCode)) {
                        $num_account_succeed += 1;
                        $postalCode = $dataBody->Object->postalCode;
                        $email = '';
                        if (isset($dataBody->Object->aiaEmailList) && count($dataBody->Object->aiaEmailList) > 0) {
                            $email = $dataBody->Object->aiaEmailList[0]->email;
                        }
                        DB::table('work_account_detail')->insert([
                            'account_number' => $account,
                            'json_body' => $response->body(),
                            'zipcode' => $postalCode,
                            'work_account_process_id' => $this->process_id,
                            'status' => 'Success',
                            'email' => $email,
                            'country' => $dataBody->Object->aiaAccountCountryCode
                        ]);
                    } else {
                        $num_account_failed += 1;
                        DB::table('work_account_detail')->insert([
                            'account_number' => $account,
                            'json_body' => $response->body(),
                            'work_account_process_id' => $this->process_id,
                            'status' => 'Failed',
                        ]);
                    }
                    Log::channel('database')->info('Successfully processed account: ' . $account, [$this->process_id]);
                }
            } catch (\Exception $ex) {
                $num_account_failed += 1;
                Log::channel('database')->error('Process Account: ' . $account . ' Error: ' . $ex->getMessage(), [$this->process_id]);
            } finally {
                DB::table('work_account_process')->where('process_id', $this->process_id)->update([
                    'num_account_succeed' => $num_account_succeed,
                    'num_account_failed' => $num_account_failed,
                    'num_account_processed' => $num_account_processed,
                ]);
                $index += 1;
            }
        }

        DB::table('work_account_process')->where('process_id', $this->process_id)
            ->update([
                'status' => 'Success',
                'date_update' => new DateTime('now')
            ]);
        Log::channel('database')->info('Processing success', [$this->process_id]);
    }

    private function readFileData($file): array
    {
        $lines = [];
        // Read file line-by-line
        $handle = fopen($file, 'r');
        if ($handle) {
            while (($line = fgets($handle)) !== false) {
                if (trim($line) !== '') {
                    $lines[] = trim($line);
                }
            }
            fclose($handle);
        }

        return $lines;
    }
}
