<?php

namespace App\Http\Controllers;

use App\Services\PriceService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BookLabelController extends Controller
{
    protected PriceService $priceService;

    // Inject PriceService vào controller
    public function __construct(PriceService $priceService)
    {
        $this->priceService = $priceService;
    }

    public function initCreateLabel(Request $request): JsonResponse
    {
        $user_id = $request->user()->id;

        // get carrier ups | usps | fedex
        $carrier = DB::table('carrier')->where('status', '=', 1)->get();

        // get holiday
        $holiday = DB::table('holiday')->get();

        // get data address book
        $addressBook = DB::table('address_book')
            ->where('user_id', '=', $user_id)->get();

        // get package saved
        $package = DB::table('package_save')
            ->where('user_id', '=', $user_id)->get();

        return response()->json([
            'carrier' => $carrier,
            'holiday' => DB::table('holiday')->get(),
            'addressBook' => $addressBook,
            'package' => $package,
            'message' => ''
        ]);
    }

    public function findServiceDetail(Request $request): JsonResponse
    {
        $user_id = $request->user()->id;

        $data = [
            'carrierId' => $request->get('carrierId'),
            'weight' => $request->get('weight'),
        ];

        $priceData = $this->priceService->getDataPriceForUser($data, $user_id);

        return response()->json($priceData);
    }
}
