<!DOCTYPE html>
<html>
<head>
    <title>From {{ $data['fromCustomer']['name'] }} To {{ $data['toCustomer']['name'] }}</title>
    <style>
        html {
            margin: 0px;
        }
        @font-face {
            font-family: 'ShipToSmall';
            src: url("{{ asset('fonts/shipping_font/ShipToSmall.ttf') }}") format('truetype');
        }
        @font-face {
            font-family: 'ShipMail';
            src: url("{{ asset('fonts/shipping_font/ShipMail.ttf') }}") format('truetype');
        }
        @font-face {
            font-family: 'ShipState';
            src: url("{{ asset('fonts/shipping_font/ShipState.ttf') }}") format('truetype');
        }
        @font-face {
            font-family: 'ShipSymbol';
            src: url("{{ asset('fonts/shipping_font/ShipSymbol.ttf') }}") format('truetype');
        }
        @font-face {
            font-family: 'ShipToBig';
            src: url("{{ asset('fonts/shipping_font/ShipToBig.ttf') }}") format('truetype');
        }
        @font-face {
            font-family: 'ShipToSmall';
            src: url("{{ asset('fonts/shipping_font/ShipToSmall.ttf') }}") format('truetype');
        }
        @font-face {
            font-family: 'ShipTracking';
            src: url("{{ asset('fonts/shipping_font/ShipTracking.ttf') }}") format('truetype');
        }
        @font-face {
            font-family: 'ShipUPS';
            src: url("{{ asset('fonts/shipping_font/ShipUPS.ttf') }}") format('truetype');
        }
        @font-face {
            font-family: 'ShipReference';
            src: url("{{ asset('fonts/shipping_font/ShipReference.ttf') }}") format('truetype');
        }
    </style>

</head>
<body>
<img src="{{ asset('storage/template/blank_non_ups.png') }}" style="margin-top: 0px; height: 100%">
{{--    ship from--}}
<div style="position: absolute; top: 6px; left: 7.5px; line-height: 0.596666em; font-size: 18px; text-transform: uppercase; font-family: ShipMail">
    {{ $data['fromCustomer']['name'] }}
    @if(isset($data['fromCustomer']['phone']) && $data['fromCustomer']['phone'] != '')
        <br>
        {{ $data['fromCustomer']['phone'] }}
    @endif
    @if(isset($data['fromCustomer']['companyName']) && $data['fromCustomer']['companyName'] != '')
        <br>
        {{ $data['fromCustomer']['companyName'] }}
    @endif
    @if(isset($data['fromCustomer']['address2']) && $data['fromCustomer']['address2'] != '')
        <br>
        {{ $data['fromCustomer']['address2'] }}
    @endif
    <br>
    {{ $data['fromCustomer']['address1'] }}
    <br>
    {{ $data['fromCustomer']['city'] }} {{ $data['fromCustomer']['state'] }} {{ $data['fromCustomer']['postalCode'] }}
</div>
{{--ship from--}}

{{--package--}}
<div style="position: absolute; top: -3px; right: 156px; font-size: 25px; font-family: ShipToBig">
    <span>{{ $data['pkgInfo']['weight'] }}</span>
</div>
<div style="position: absolute; top: 17.5px; left: 269px; font-size: 20px; font-family: ShipMail">
    {{ $data['pkgInfo']['length'] }}, {{ $data['pkgInfo']['width'] }}, {{ $data['pkgInfo']['height'] }}
</div>
{{--package--}}

{{--    ship to--}}
<div style="position: absolute; top: 84px; left: 25px; line-height: 0.4416666em; font-size: 32.5px; text-transform: uppercase; font-family: ShipToSmall">
    {{ $data['toCustomer']['name'] }}
    @if(isset($data['toCustomer']['phone']) && $data['toCustomer']['phone'] != '')
        <br>
        {{ $data['toCustomer']['phone'] }}
    @endif

    @if(isset($data['toCustomer']['companyName']) && $data['toCustomer']['companyName'] != '')
        <br>
        {{ $data['toCustomer']['companyName'] }}
    @endif
    @if(isset($data['toCustomer']['address2']) && $data['toCustomer']['address2'] != '')
        <br>
        {{ $data['toCustomer']['address2'] }}
    @endif
    <br>
    {{ $data['toCustomer']['address1'] }}
    <br>
    <div style="height: 4px"></div>
    <span style="font-family: ShipToBig; font-size: 33.5px">
            {{ $data['toCustomer']['city'] }} {{ $data['toCustomer']['state'] }} {{ $data['toCustomer']['postalCode'] }}
        </span>
</div>
{{--    ship to--}}

{{--    maxi code--}}
<div style="position: absolute; top: 203px;">
    @if($data['pkgInfo']['weight'] >= 70)
        <div style="position: absolute; top: -40px; left: 18px">
            <span style="font-size: 175px; font-family: ShipState">H</span>
        </div>
    @else
        <img src="{{ asset($barcode) }}" style="margin-left: 7px; margin-top: 0px; height: 27%" alt="maxicode">
    @endif
</div>
{{--    maxi code--}}

{{--    routeCode--}}
<div style="position: absolute; top: 193px; left: 125px">
    <span style="font-size: 57px; font-family: ShipState">{{ $data['routeCode'] }}</span>
    <div style="margin-top: -2px; margin-left: 10px">{!! DNS1D::getBarcodeHTML('420' . $data['toCustomer']['postalCode'], "C128",1.5,50) !!}</div>
</div>
{{--    routeCode--}}

{{--    serveice name--}}
<div style="position: absolute; top: 314px; font-size: 25px; left: 3px; font-family: ShipUPS">
    <span style="text-transform: uppercase;">{{ $data['lblservicename'] }}</span>
</div>
{{--    tracking no--}}
<div style="position: absolute; top: 344px; font-size: 14px; margin-left: 86px; font-family: ShipTracking">
    <span>
        {{ substr($data['tracking_no'], 0, 2) }}
        {{ substr($data['tracking_no'], 2, 3) }}
        {{ substr($data['tracking_no'], 5, 3) }}
        {{ substr($data['tracking_no'], 8, 2) }}
        {{ substr($data['tracking_no'], 10, 4) }}
        {{ substr($data['tracking_no'], 14, 4) }}
        {{ substr($data['tracking_no'], 18) }}
    </span>
</div>
{{--    tracking no--}}
<div style="position: absolute; left: 302px; top: 303px; font-size: 66px; font-family: ShipSymbol">
    @if(strtolower($data['lblservicename']) == 'next day air' || strtolower($data['lblservicename']) == 'ups next day air')
        @if( $data['pkgInfo']['saturdayDelivery'])
            <span>1</span>
            <span style="margin-left: -3px">S</span>
        @else
            1
        @endif
    @endif
    @if(strtolower($data['lblservicename']) == 'ups next day air early')
        @if( $data['pkgInfo']['saturdayDelivery'])
            <span>1</span>
            <span style="margin-left: -7px">+</span>
            <span style="margin-left: -10px">S</span>
        @else
            <span>1</span>
            <span style="margin-left: -7px">+</span>
        @endif
    @endif
</div>
{{--service name--}}

<div style="position: absolute; top: 370px; width: 110%; margin: 4px -1px;">
    <div style="margin-top: 1px; margin-left: 25px">{!! DNS1D::getBarcodeHTML($data['tracking_no'], "C128",1.5,100) !!}</div>
</div>

<div style="position: absolute; top: 545px; width: 50%; font-family: ShipState">
    @if($data['pkgInfo']['reference1'])
        <div style="margin-left: 5px; font-size: 13px;">Reference No.1: {{ $data['pkgInfo']['reference1'] }}</div>
    @endif
</div>
<div style="position: absolute; top: 565px; left: 28%; width: 100%; font-size: 14px; font-family: ShipReference">
    <div style="position: absolute;">XOL 24.04.04</div>
    <div style="position: absolute; left: 80px;">NV45 15.0A 04/2024*</div>
</div>
</body>
</html>
