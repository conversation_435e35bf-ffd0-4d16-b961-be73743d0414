<?php

namespace App\Http\Controllers;

use DateTime;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class FAQsController extends Controller
{
    public function getFAQs(Request $request): JsonResponse
    {
	    $query_param = $request->all();
	    $query = DB::table('faqs');
	    if(isset($query_param['q'])) {
		    $query->where(function ($q) use ($query_param) {
			    $q->where("subject", 'like', '%' . $query_param["q"] . '%')
				    ->orWhere("content", 'like', '%' . $query_param["q"] . '%');
		    });
	    }
	    $query->orderByDesc('create_at');
	    $data_faqs = $query->paginate($query_param['itemsPerPage'] ?? 10);
	    return response()->json([
		    'faqs' => $data_faqs,
		    'message' => ''
	    ]);
    }
	
	public function getFAQsUser(Request $request): JsonResponse
	{
		$query_param = $request->all();
		$query = DB::table('faqs')->where('status', '=', 1);
		if(isset($query_param['q'])) {
			$query->where(function ($q) use ($query_param) {
				$q->where("subject", 'like', '%' . $query_param["q"] . '%')
					->orWhere("content", 'like', '%' . $query_param["q"] . '%');
			});
		}
		$query->orderByDesc('create_at');
		$data_faqs = $query->get();
		return response()->json([
			'faqs' => $data_faqs,
			'message' => ''
		]);
	}
	
	/**
	 * deleteAddressBook
	 * @param $id
	 * @return JsonResponse
	 */
	public function deleteFAQs($id): JsonResponse
	{
		DB::table('faqs')->where('id', '=', $id)->delete();
		return response()->json([
			'status' => true
		]);
	}
	
	/**
	 * updateAddressBook
	 * @param Request $request
	 * @return JsonResponse
	 * @throws \Exception
	 */
	public function updateFAQs(Request $request): JsonResponse
	{
		$data = $request->all();
		$dataUpdate = [
			'subject' => $data['subject'],
			'content' => $data['content'],
			'status' => $data['status'],
			'create_at' =>  new DateTime(('now'))
		];
		if(isset($data['id'])) {
			DB::table('faqs')->where('id', '=', $data['id'])
				->update($dataUpdate);
		} else {
			DB::table('faqs')->insert($dataUpdate);
		}
		return response()->json([
			'status' => true
		]);
	}
	
	public function updateStatus(Request $request): JsonResponse
	{
		$data = $request->all();
		DB::table('faqs')->where('id', '=', $data['id'])
			->update(['status' => $data['status']]);
		return response()->json([
			'status' => true
		]);
	}
}
