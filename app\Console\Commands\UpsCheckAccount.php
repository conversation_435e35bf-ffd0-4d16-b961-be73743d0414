<?php

namespace App\Console\Commands;

use App\Jobs\CheckAccount;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpsCheckAccount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:ups-check-account';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
//        $dataAccount = DB::table('work_account_process')
//            ->where('status', '=', 'New')
//            ->orWhere('is_failed', '=', 1)
//            ->get();
        $sql = "SELECT * FROM work_account_process
                where action = 'run' and (`status` = 'New' or is_failed = 1)";
        $dataAccount = DB::select($sql);
        foreach ($dataAccount as $account) {
            Log::channel('database')->info('Add task Process ID: ' . $account->process_id);
            DB::table('work_account_process')->where('process_id', $account->process_id)->update(['is_failed' => 0]);
            CheckAccount::dispatch($account->process_id);
        }
    }
}
