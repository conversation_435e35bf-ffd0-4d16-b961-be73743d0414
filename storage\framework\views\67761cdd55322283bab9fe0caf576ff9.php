<!DOCTYPE html>
<html>
<head>
    <title>USPS Label - From <?php echo e($data['from']['name']); ?> To <?php echo e($data['to']['name']); ?></title>
    <style>
        html, body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            width: 4in;
            height: 6in;
        }
        
        .label-container {
            width: 100%;
            height: 100%;
            border: 2px solid #000;
            position: relative;
            box-sizing: border-box;
        }
        
        /* Header section with G logo and service info */
        .header-section {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            height: 80px;
            border-bottom: 1px solid #000;
        }
        
        .g-logo {
            position: absolute;
            left: 10px;
            top: 10px;
            width: 60px;
            height: 60px;
            border: 2px solid #000;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            font-weight: bold;
        }
        
        .service-info {
            position: absolute;
            right: 10px;
            top: 10px;
            text-align: center;
            font-size: 10px;
            line-height: 1.2;
            border: 1px solid #000;
            padding: 5px;
            width: 120px;
        }
        
        .service-title {
            position: absolute;
            left: 90px;
            top: 25px;
            font-size: 14px;
            font-weight: bold;
        }
        
        /* From section */
        .from-section {
            position: absolute;
            top: 100px;
            left: 10px;
            right: 10px;
            height: 80px;
        }
        
        .from-label {
            font-size: 10px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .from-info {
            font-size: 11px;
            line-height: 1.3;
        }
        
        .mailed-from {
            position: absolute;
            right: 10px;
            top: 0;
            font-size: 10px;
            text-align: right;
        }
        
        /* To section */
        .to-section {
            position: absolute;
            top: 190px;
            left: 10px;
            right: 10px;
            height: 100px;
            border-bottom: 2px solid #000;
        }
        
        .to-label {
            font-size: 10px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .to-info {
            font-size: 14px;
            line-height: 1.3;
            font-weight: bold;
        }
        
        /* Tracking section */
        .tracking-section {
            position: absolute;
            top: 300px;
            left: 10px;
            right: 10px;
            height: 120px;
            text-align: center;
        }
        
        .tracking-title {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .tracking-barcode {
            margin: 10px 0;
        }
        
        .tracking-number {
            font-size: 11px;
            font-weight: bold;
            margin-top: 5px;
            letter-spacing: 2px;
        }
        
        /* Bottom section */
        .bottom-section {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            height: 30px;
            text-align: center;
        }
        
        .ap-code {
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="label-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="g-logo">G</div>
            <div class="service-info">
                USPS GROUND<br>
                ADVANTAGE<br>
                U.S. POSTAGE PAID<br>
                ATFM<br>
                e-Postage
            </div>
            <div class="service-title">USPS GROUND ADVANTAGE ™</div>
        </div>
        
        <!-- From Section -->
        <div class="from-section">
            <div class="from-info">
                <?php echo e($data['from']['company'] ?? $data['from']['name']); ?><br>
                <?php echo e($data['from']['address1']); ?><br>
                <?php if(!empty($data['from']['address2'])): ?>
                    <?php echo e($data['from']['address2']); ?><br>
                <?php endif; ?>
                <?php echo e($data['from']['city']); ?> <?php echo e($data['from']['state']); ?> <?php echo e($data['from']['zip']); ?>

            </div>
            <div class="mailed-from">
                <?php echo e(date('m/d/y')); ?><br>
                Mailed From <?php echo e($data['from']['zip']); ?><br>
                WT: <?php echo e($data['weight'] ?? '0.00'); ?> oz
            </div>
        </div>
        
        <!-- To Section -->
        <div class="to-section">
            <div class="to-label">SHIP TO:</div>
            <div class="to-info">
                <?php echo e($data['to']['name']); ?><br>
                <?php echo e($data['to']['address1']); ?><br>
                <?php if(!empty($data['to']['address2'])): ?>
                    <?php echo e($data['to']['address2']); ?><br>
                <?php endif; ?>
                <?php echo e($data['to']['city']); ?> <?php echo e($data['to']['state']); ?> <?php echo e($data['to']['zip']); ?>

            </div>
        </div>
        
        <!-- Tracking Section -->
        <div class="tracking-section">
            <div class="tracking-title">USPS TRACKING # EP</div>
            <div class="tracking-barcode">
                <?php echo DNS1D::getBarcodeHTML($data['tracking_number'], "C128", 2, 60); ?>

            </div>
            <div class="tracking-number"><?php echo e($data['tracking_number']); ?></div>
        </div>
        
        <!-- Bottom Section -->
        <div class="bottom-section">
            <div class="ap-code">AP</div>
        </div>
    </div>
</body>
</html>
<?php /**PATH C:\source\starter-kit-api\resources\views/template_pdf/usps_label.blade.php ENDPATH**/ ?>