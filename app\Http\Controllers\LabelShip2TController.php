<?php

namespace App\Http\Controllers;

use DateTime;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManagerStatic as Image;

class LabelShip2TController extends Controller
{
    public static function getLabelShip2T(Request $request): JsonResponse|array
    {
        $url_get_label = env('URL_API_LABEL2_SHIP2T');

        $data = $request->all();

        $data['user_id'] = 'test1';

        $settings_account_number = DB::connection('mysql_shipease')->table('settings_ship2t')->orderBy('order_no')->get();
        $list_account = DB::connection('mysql_shipease')->table('account_number_ship2t')->orderBy('order_no')->get();
        $index_account = $settings_account_number[0]->value_;
        $limit_time = $settings_account_number[1]->value_;

        $sql_checkExecute = "select * from user_limit WHERE user_name = :user_id
		and NOW() >= DATE_ADD(create_date,INTERVAL :limit_time SECOND)";

        $user_limit = DB::connection('mysql_shipease')->select($sql_checkExecute, [
            'user_id' => $data['user_id'],
            'limit_time' => $limit_time
        ]);

        if (count($user_limit) <= 0) {
            return response()->json([
                'error' => true,
                'message' => 'Cannot create label. Please try again ' . $limit_time . '(s)'
            ]);
        }

        $account = null;
        $count_while = $index_account;
        while ($account == null) {
            $sql = "SELECT account_number.account_no
                    FROM account_number_ship2t account_number
                    where account_number.order_no = :index";
            $data_account = DB::connection('mysql_shipease')->selectOne($sql, [
                'index' => $index_account
            ]);

            if ($data_account) {
                $account = $data_account->account_no;
            }
            $index_account += 1;
            if ($index_account > count($list_account)) {
                $index_account = 0;
            }
            if ($count_while > count($list_account) * 2) {
                break;
            }
            $count_while += 1;
        }
        if ($index_account >= count($list_account)) {
            $index_account = 0;
        }

        $data['account_no'] = $account;

        $response = Http::post($url_get_label . "/create", $data);

        $dataBody = json_decode($response->body());
        $labelBase64 = $dataBody->label;
        $file_name = '/orders/' . $data['user_id'] . '/' . uniqid() . '.png';
        Storage::disk('public')->put($file_name, base64_decode($labelBase64));

        LabelController::convertImage($file_name);

        // convert image to pdf
        $format_date = date_format(new DateTime(('now')), 'Ymdhms');
        $pdf_file_name = '/orders/' . $data['user_id'] . '/' . uniqid() . '_' . $format_date . '.pdf';

        $data['routeCode'] = $dataBody->routeCode;
        $data['lblservicename'] = $dataBody->lblservicename;
        $data['label_image'] = $file_name;
        $data['tracking_no'] = $dataBody->tracking;
        $data['pkgInfo']['saturdayDelivery'] = $data['satDelivery'];

        PdfController::genPDFLabelApi2Shipease($data, Storage::disk('public')->path($pdf_file_name));

        Storage::disk('public')->delete($file_name);

        // update index account
        DB::connection('mysql_shipease')->table('settings_ship2t')->where('key_', '=', 'index_account')
            ->update([
                'value_' => $index_account,
            ]);

        DB::connection('mysql_shipease')->table('user_limit')->where('user_name', '=', $data['user_id'])
            ->delete();

        DB::connection('mysql_shipease')->table('user_limit')
            ->insert([
                'user_name' => $data['user_id'],
            ]);

        DB::connection('mysql_shipease')->table('history_label_ship2t')
            ->insert([
                'from'          => $data['fromCustomer']['name'],
                'to'            => $data['toCustomer']['name'],
                'service'       => $data['pkgInfo']['serviceName'],
                'tracking'      => $dataBody->tracking,
                'type'          => 'label1',
                'create_at'     => new DateTime(),
                'url_label'     => $pdf_file_name
            ]);

        $dataAddressBook = [];

        if ($data["saveAddressFrom"]) {
            $dataAddressBook[] = [
                "country" => $data['fromCustomer']["country"],
                "name" => $data['fromCustomer']["name"],
                "company_name" => $data['fromCustomer']["companyName"],
                "address1" => $data['fromCustomer']["address1"],
                "address2" => $data['fromCustomer']["address2"],
                "city" => $data['fromCustomer']["city"],
                "state" => $data['fromCustomer']["state"],
                "zipcode" => $data['fromCustomer']["postalCode"],
                "phone" => $data['fromCustomer']["phone"],
            ];
        }

        if ($data["saveAddressTo"]) {
            $dataAddressBook[] = [
                "country" => $data['toCustomer']["countryCode"],
                "name" => $data['toCustomer']["name"],
                "company_name" => $data['toCustomer']["companyName"],
                "address1" => $data['toCustomer']["address1"],
                "address2" => $data['toCustomer']["address2"],
                "city" => $data['toCustomer']["city"],
                "state" => $data['toCustomer']["state"],
                "zipcode" => $data['toCustomer']["postalCode"],
                "phone" => $data['toCustomer']["phone"],
            ];
        }
        DB::connection('mysql_shipease')->table('address_book_ship2t')->insert($dataAddressBook);
        return [
            'tracking_no' => $dataBody->tracking,
            'file_name' => $pdf_file_name
        ];
    }

    public function getOrder(Request $request): JsonResponse
    {
        $query = DB::connection('mysql_shipease')->table('history_label_ship2t')->orderByDesc('create_at');
        if ($request->query('search')) {
            $keySearch = $request->query('search');
            $query->where("to", 'like', '%' . $keySearch . '%')
                ->orWhere("from", 'like', '%' . $keySearch . '%')
                ->orWhere("tracking", 'like', '%' . $keySearch . '%');
        }
        $data = $query->paginate($request->query('limit') ?? 10);
        return response()->json($data);
    }

    public function saveOrder(Request $request): JsonResponse
    {
        $data = $request->all();
        $data['user_id'] = 'test1';
        $dataAddressBook = [];

        if ($data["saveAddressFrom"]) {
            $dataAddressBook[] = [
                "country" => $data["fromCustomerCountry"],
                "name" => $data["fromCustomerName"],
                "company_name" => $data["fromCustomercompanyName"],
                "address1" => $data["fromCustomerAddress1"],
                "address2" => $data["fromCustomerAddress2"],
                "city" => $data["fromCustomerCity"],
                "state" => $data["fromCustomerState"],
                "zipcode" => $data["fromCustomerZip"],
                "phone" => $data["fromCustomerPhone"],
            ];
        }

        if ($data["saveAddressTo"]) {
            $dataAddressBook[] = [
                "country" => $data["toCustomerCountry"],
                "name" => $data["toCustomerName"],
                "company_name" => $data["toCustomercompanyName"],
                "address1" => $data["toCustomerAddress1"],
                "address2" => $data["toCustomerAddress2"],
                "city" => $data["toCustomerCity"],
                "state" => $data["toCustomerState"],
                "zipcode" => $data["toCustomerZip"],
                "phone" => $data["toCustomerPhone"],
            ];
        }
        DB::connection('mysql_shipease')->table('address_book')->insert($dataAddressBook);

        // insert order

        $file_name = '/orders/' . $data['user_id'] . '/' . uniqid() . '.png';
        Storage::disk('public')->put($file_name, base64_decode($data['label']));

        if (isset($data['label2'])) {
            $this->convertImage90deress($file_name);
        }

        $dataOrder = [
            'from'          => $data['fromCustomerName'],
            'to'            => $data['toCustomerName'],
            'service'       => $data['pkgInfoService'],
            'tracking'      => $data['trackingnumber'],
            'url_label'     => '/storage' . $file_name,
            'type'          => 'label2',
            'create_at'     => new DateTime('now')
        ];
        DB::connection('mysql_shipease')->table('history_label_ship2t')->insert($dataOrder);
        return response()->json(true);
    }

    private function convertImage90deress($file_name): void
    {

        $url = Storage::disk('public')->path($file_name);

        // Load the image using Intervention/Image package
        $image = Image::make($url);

        // Rotate the image (you can specify the rotation angle)
        $rotatedImage = $image->rotate(-90);
        $rotatedImage->save($url);
    }

    public function bookLabel(Request $request): JsonResponse
    {
        $data = $request->all();
        $url_get_label = env('URL_API_LABEL2');

        $account = DB::table('settings')->where('key', '=', 'Account 2')->first();
        if (!$account) {
            return response()->json(['message' => 'Account 2 not found'], 400);
        }

        $data['user_id'] = 999;
        $new_date = new DateTime(('now'));
        $order_id = uniqid() . '_' . date_format($new_date, 'YMdhms');

        DB::table('orders')->insert([
            'id' => $order_id,
            'date_create' => new \DateTime(('now')),
            'from_name' => $data['dataFrom']['name'],
            'to_name' => $data['dataTo']['name'],
            'no_of_package' => 1,
            'weight' => $data['package']['weight'],
            'length' => $data['package']['length'],
            'width' => $data['package']['width'],
            'height' => $data['package']['height'],
            'ref1' => $data['package']['ref1'] ?? '',
            'ref2' => $data['package']['ref2'] ?? '',
            'saturday_delivery' => $data['package']['saturdayDelivery'] ? 1 : 0,
            'signature' => $data['package']['signature'] ? 1 : 0,
            'service_id' => $data['service_id'],
            'to_company' => $data['dataTo']['companyName'],
            'to_phone' => $data['dataTo']['phone'],
            'to_country' => 'US',
            'to_address1' => $data['dataTo']['address1'],
            'to_address2' => $data['dataTo']['address2'],
            'to_zip' => $data['dataTo']['zip'],
            'to_city' => $data['dataTo']['city'],
            'to_state' => $data['dataTo']['state'],
            'from_company' => $data['dataFrom']['companyName'],
            'from_country' => 'US',
            'from_phone' => $data['dataFrom']['phone'],
            'from_address1' => $data['dataFrom']['address1'],
            'from_address2' => $data['dataFrom']['address2'],
            'from_zip' => $data['dataFrom']['zip'],
            'from_city' => $data['dataFrom']['city'],
            'from_state' => $data['dataFrom']['state'],
            'status' => 3,
            'price' => 1,
            'user_id' => $data['user_id'],
            'tracking_status' => 'Pre_transit',
            'create_at' => new \DateTime(('now')),
            'discount' => $data['discount'] ?? 0,
            'total_price' => 1
        ]);

        $response = Http::post($url_get_label . "/create", [
            'pkgInfo' => [
                'serviceName' => $data['service_name'],
                'countryCode' => 'US',
                'Signature' => $data['package']['signature'],
                'carbon' => false,
                'demoFlag' => false,
                'reference1' => $data['package']['ref1'] ?? '',
                'reference2' => $data['package']['ref2'] ?? '',
                'weight' => $data['package']['weight'],
                'length' => $data['package']['length'],
                'width' => $data['package']['width'],
                'height' => $data['package']['height'],
                'barcoderef' => false
            ],
            'fromCustomer' => [
                'address1' => $data['dataFrom']['address1'],
                'address2' => $data['dataFrom']['address2'] ?? '',
                'address3' => '',
                'city' => $data['dataFrom']['city'],
                'state' => $data['dataFrom']['state'],
                'name' => $data['dataFrom']['name'],
                'email' => '',
                'postalCode' => $data['dataFrom']['zip'],
                'companyName' => $data['dataFrom']['companyName'] ?? '',
                'phone' => $data['dataFrom']['phone'] ?? '',
                'fax' => '',
                'country' => 'United States',
                'countryName' => 'US',
            ],
            'toCustomer' => [
                'companyName' => $data['dataTo']['companyName'] ?? '',
                'name' => $data['dataTo']['name'],
                'address1' => $data['dataTo']['address1'],
                'address2' => $data['dataTo']['address2'] ?? '',
                'address3' => '',
                'city' => $data['dataTo']['city'],
                'state' => $data['dataTo']['state'],
                'email' => '',
                'fax' => '',
                'phone' => $data['dataTo']['phone'] ?? '',
                'postalCode' => $data['dataTo']['zip'],
                'countryCode' => 'United States',
                'res' => false
            ],
            'accountNumber' => 'real',
            'satDelivery' => $data['package']['saturdayDelivery'],
            'account_no' => $account->value
        ]);

        $dataBody = json_decode($response->body());
        $labelBase64 = $dataBody->label;
        $file_name = '/orders/' . $data['user_id'] . '/' . uniqid() . '.png';
        Storage::disk('public')->put($file_name, base64_decode($labelBase64));

        LabelController::convertImage($file_name);

        // convert image to pdf
        $format_date = date_format(new DateTime(('now')), 'Ymdhms');
        $pdf_file_name = '/orders/' . $data['user_id'] . '/' . uniqid() . '_' . $format_date . '.pdf';

        $data['routeCode'] = $dataBody->routeCode;
        $data['lblservicename'] = $dataBody->lblservicename;
        $data['label_image'] = $file_name;
        $data['tracking_no'] = $dataBody->tracking;

        // PdfController::createPDFV2('storage' . $file_name, Storage::disk('public')->path($pdf_file_name));
        PdfController::genPDFCrushippers($data, Storage::disk('public')->path($pdf_file_name));

        Storage::disk('public')->delete($file_name);

        if ($dataBody->tracking == '') {
            return response()->json([
                'status' => 422,
                'message' => 'Cannot create label. Please try again'
            ]);
        }

        DB::table('orders')->where('id', '=', $order_id)
            ->update([
                'status' => 1,
                'tracking_id' => $dataBody->tracking,
                'label_image' => $pdf_file_name
            ]);

        $labelImage[] = $pdf_file_name;

        return response()->json([
            'status' => 200,
            'lable_url' => $pdf_file_name
        ]);
    }

    public function getTrackerUser() {
        $dataHistory = [];
        $history_login = DB::table('login_history')->where('user_id', '=', 999)->get();

        foreach($history_login as $history) {
            $dataHistory[] = [
                'id' => $history->id,
                'user_id' => 'user1',
                'content' => 'User1 login to system - IP: ' . $history->ip . ' - Location: ' . $history->location,
                'date_add' => $history->create_at
            ];
        }

        $orders = DB::table('orders')->where('user_id', '=', 999)->get();
        foreach($orders as $order) {
            $dataHistory[] = [
                'id' => $order->id,
                'user_id' => 'user1',
                'content' => 'User1 create label for ' . $order->to_name . ' - Tracking: ' . $order->tracking_id,
                'date_add' => $order->create_at
            ];
        }

        usort($dataHistory, function($a, $b) {
            return $a['date_add'] <=> $b['date_add'];
        });

        return response()->json($dataHistory);
    }
}
