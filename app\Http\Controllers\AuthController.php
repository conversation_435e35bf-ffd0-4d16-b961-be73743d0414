<?php

namespace App\Http\Controllers;

use App\Mail\VerifyAccount;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use DateTime;
use Jen<PERSON>gers\Agent\Facades\Agent;
use <PERSON><PERSON>an\Location\Facades\Location;
use <PERSON><PERSON>an\Location\Position;

class AuthController extends Controller
{
  /**
   * Create user
   *
   * @param  [string] name
   * @param  [string] email
   * @param  [string] password
   * @param  [string] password_confirmation
   * @return [string] message
   */
  public function register(Request $request): JsonResponse
  {
    $data = request()->all();

    $rules = [
      'name' => 'required|unique:users',
      'email' => 'required|email|unique:users',
      'password' => 'required|min:6',
    ];

    $validator = Validator::make($data, $rules, $arrMsg ?? []);

    if ($validator->fails()) {
      return response()->json([
        'status' => 404,
        'message' => $validator->errors(),
      ]);
    }

    $verify_code = mt_rand(100000, 999999);
    $token = $unique = substr(base64_encode(md5(mt_rand())), 0, 64);
    DB::table('users')->insert([
      'name' => $request->name,
      'email' => $request->email,
      'password' => bcrypt($request->password),
      'status' => 0,
      'totalSpend' => 0,
      'balance' => 0,
      'is_admin' => 0,
      'is_email_verified' => 1,
      'verify_code' => $verify_code,
      'created_at' => new DateTime('now'),
      'is_disabel_price' => 0,
      'token_mail' => $token
    ]);
//    Mail::to($request->email)->send(new VerifyAccount($verify_code));
    return response()->json([
      'token' => $token,
      'message' => 'Successfully created user!',
    ], 201);
  }

  /**
   * Login user and create token
   *
   * @param  [string] email
   * @param  [string] password
   * @param  [boolean] remember_me
   */
  public function login(Request $request): JsonResponse
  {
    $request->validate([
      'email' => 'required|string',
      'password' => 'required|string',
      'remember_me' => 'boolean',
    ]);

    $login = $request->input('email');
    $type = filter_var($login, FILTER_VALIDATE_EMAIL) ? 'email' : 'name';
    $request->merge([
      $type => $login,
      'password' => $request['password']
    ]);

    $credentials = $request->only($type, 'password');

    $userVerify = DB::table('users')->where($type, '=', $login)->first();
    if ($userVerify && $userVerify->is_email_verified === 0) {
      return response()->json([
        'status' => 404,
        'token' => $userVerify->token_mail,
        'message' => 'Unauthorized'
      ]);
    }

    if ($userVerify && $userVerify->status === 0) {
      return response()->json([
        'status' => 422,
        'message' => 'Please contact admin to unlock your account' . PHP_EOL . 'Send your detail <NAME_EMAIL>'
      ]);
    }

    if (Auth::attemptWhen($credentials, function (User $user) {
      return $user->is_email_verified === 1 && $user->status === 1;
    })) {
      $user = Auth::user();
      $tokenResult = $user->createToken('Personal Access Token');
      $token = $tokenResult->plainTextToken;

      $desktop_device = "";
      if (Agent::isDesktop()) {
        $desktop_device = "PC";
      } elseif (Agent::isTablet()) {
        $desktop_device = "Tablet";
      } elseif (Agent::isPhone()) {
        $desktop_device = "Mobile";
      }
      DB::table('login_history')->insert([
        'user_id' => $user->id,
        'browser' => Agent::browser(),
        'device' => Agent::device(),
        'location' => $this->getLocation($_SERVER['HTTP_CF_CONNECTING_IP'] ?? $_SERVER['REMOTE_ADDR']),
        'ip' => $_SERVER['HTTP_CF_CONNECTING_IP'] ?? $_SERVER['REMOTE_ADDR'],
        'platform' => Agent::platform(),
        'desktop_device' => $desktop_device,
        'create_at' => new DateTime('now')
      ]);

      return response()->json([
        'accessToken' => $token,
        'userData' => $user,
        'token_type' => 'Bearer',
      ]);
    }

    return response()->json([
      'message' => 'Unauthorized'
    ], 422);
  }


  public function forgotPassword(Request $request): JsonResponse
  {
    $request->validate([
      'email' => 'required|string|email',
    ]);
    $data = $request->all();
    $user = User::where('email', $data['email'])->first();
    if (!$user) {
      return response()->json([
        'status' => 404,
        'message' => 'Email address not found'
      ]);
    }
    $token = $unique = substr(base64_encode(md5(mt_rand())), 0, 64);
    $verify_code = mt_rand(100000, 999999);

    Mail::to($user->email)->send(new VerifyAccount($verify_code));

    DB::table('users')->where('id', '=', $user->id)
      ->update([
        'token_mail' => $token,
        'verify_code' => $verify_code
      ]);

    return response()->json([
      'status' => 200,
      'token' => $token,
      'message' => ''
    ]);
  }

  public function loginAdmin(Request $request): JsonResponse
  {
    $request->validate([
      'email' => 'required|string',
      'password' => 'required|string',
      'remember_me' => 'boolean',
    ]);

    $login = $request->input('email');
    $type = filter_var($login, FILTER_VALIDATE_EMAIL) ? 'email' : 'name';
    $request->merge([
      $type => $login,
      'password' => $request['password']
    ]);

    $credentials = $request->only($type, 'password');
    if (Auth::attemptWhen($credentials, function (User $user) {
      return $user->is_admin === 1;
    })) {
      $user = Auth::user();
      $tokenResult = $user->createToken('Personal Access Token');
      $token = $tokenResult->plainTextToken;

      $desktop_device = "";
      if (Agent::isDesktop()) {
        $desktop_device = "PC";
      } elseif (Agent::isTablet()) {
        $desktop_device = "Tablet";
      } elseif (Agent::isPhone()) {
        $desktop_device = "Mobile";
      }
      DB::table('login_history')->insert([
        'user_id' => $user->id,
        'browser' => Agent::browser(),
        'device' => Agent::device(),
        'location' => $this->getLocation($_SERVER['HTTP_CF_CONNECTING_IP'] ?? $_SERVER['REMOTE_ADDR']),
        'ip' => $_SERVER['HTTP_CF_CONNECTING_IP'] ?? $_SERVER['REMOTE_ADDR'],
        'platform' => Agent::platform(),
        'desktop_device' => $desktop_device,
        'create_at' => new DateTime('now')
      ]);

      return response()->json([
        'accessToken' => $token,
        'userData' => $user,
        'token_type' => 'Bearer',
      ]);
    }

    return response()->json([
      'message' => 'Unauthorized'
    ], 422);
  }

  public function verifyAccount(Request $request): JsonResponse
  {
    $request->validate([
      'code' => 'required|string',
      'token' => 'required|string',
    ]);
    $data = $request->all();

    $user = DB::table('users')->where('token_mail', '=', $data['token'])
      ->first();
    if (!$user) {
      return response()->json([
        'status' => 404,
        'message' => 'Token is valid'
      ]);
    }
    if ($user->verify_code != $data['code']) {
      return response()->json([
        'status' => 404,
        'message' => "That code didn't work. Check the code and try again"
      ]);
    }

    if (isset($data['type']) && $data['type'] === 'reset_password') {
      DB::table('users')->where('id', '=', $user->id)
        ->update([
          'verify_code' => null,
        ]);
    } else {
      DB::table('users')->where('id', '=', $user->id)
        ->update([
          'email_verified_at' => new DateTime('now'),
          'is_email_verified' => 1,
          'verify_code' => null,
          'token_mail' => null
        ]);
    }

    return response()->json([
      'status' => 200,
      'message' => ""
    ]);
  }

  public function resetPassword(Request $request): JsonResponse
  {
    $request->validate([
      'token' => 'required|string',
      'password' => 'required'
    ]);
    $data = $request->all();

    $user = DB::table('users')->where('token_mail', '=', $data['token'])
      ->first();
    if (!$user) {
      return response()->json([
        'status' => 404,
        'message' => 'Token is valid'
      ]);
    }
    DB::table('users')->where('id', '=', $user->id)
      ->update([
        'password' => bcrypt($data['password']),
        'token_mail' => null
      ]);
    return response()->json([
      'status' => 200,
      'message' => ""
    ]);
  }

  public function resendCodeVerify(Request $request): JsonResponse
  {
    $request->validate([
      'token' => 'required|string',
    ]);
    $data = $request->all();
    $user = DB::table('users')->where('token_mail', '=', $data['token'])
      ->first();
    if (!$user) {
      return response()->json([
        'status' => 404,
        'message' => 'Token is valid'
      ]);
    }
    $verify_code = mt_rand(100000, 999999);

    Mail::to($user->email)->send(new VerifyAccount($verify_code));

    DB::table('users')->where('id', '=', $user->id)
      ->update([
        'verify_code' => $verify_code
      ]);
    return response()->json([
      'status' => 200,
      'message' => "Code is resend success. Please check again"
    ]);
  }

  /**
   * Get the authenticated User
   *
   * @return [json] user object
   */
  public function user(Request $request)
  {
    $notification = DB::table('settings')->where('key', '=', 'Notification')->first();
    return response()->json([
      'user' => $request->user(),
      'notification' => $notification->value ?? ''
    ]);
  }

  /**
   * Logout user (Revoke the token)
   *
   * @return [string] message
   */
  public function logout(Request $request): JsonResponse
  {
    $request->user()->tokens()->delete();

    return response()->json([
      'message' => 'Successfully logged out'
    ]);
  }

  public function userList(Request $request): JsonResponse
  {
    $data = $request->all();

    $query = DB::table('users')
      ->leftJoin('orders', 'orders.user_id', '=', 'users.id');

    if (isset($data["status"])) {
      $query->where("users.status", '=', $data["status"]);
    }
    if (isset($data["q"])) {
      $query->where(function ($q) use ($data) {
        $q->where("users.name", 'like', '%' . $data["q"] . '%')
          ->orWhere("users.email", 'like', '%' . $data["q"] . '%');
      });
    }

    $query->groupBy(
      'users.id',
      'users.name',
      'users.email',
      'users.created_at',
      'users.status',
      'users.balance',
      'users.is_disabel_price',
    );

    $query->where("is_admin", '<>', 1)->select(
      'users.id',
      'users.name as username',
      'users.email',
      'users.created_at',
      'users.status',
      'users.balance',
      'users.is_disabel_price',
    );
    $query->selectRaw('NULLIF(sum(price),0) as totalSpend');
    $query->selectRaw('count(orders.id) AS totalOrder');
    $dataTable = $query->paginate($data['itemsPerPage']);

    return response()->json([
      'users' => $dataTable,
      'message' => ''
    ]);
  }

  public function userUpdate($id, Request $request): JsonResponse
  {
    $data = $request->all();
    DB::table('users')->where('id', '=', $id)->update($data);
    if (isset($data['status']) && $data['status'] == 0) {
      $user = User::where('id', $id)->first();
      if ($user->tokens()) {
        $user->tokens()->delete();
      }
    }
    return response()->json([
      'message' => 'Update success',
      'status' => 200
    ]);
  }

  public function userDelete(Request $request)
  {
    $data = $request->all();
    if (isset($data["data_update"])) {
      foreach ($data["data_update"] as $item) {
        if ($item == 1) {
          continue;
        }
        DB::table("users")
          ->where("id", "=", $item)
          ->delete();
      }
    }
    return response()->json([
      'status' => true
    ]);
  }

  public function updateStatusAllUser(Request $request): JsonResponse
  {
    $data = $request->all();
    if (isset($data["dataUpdate"])) {
      foreach ($data["dataUpdate"] as $item) {
        DB::table("users")
          ->where("id", "=", $item['id'])
          ->update([
            'status' => $item['status']
          ]);

        if ($item['status'] == 0) {
          $user = User::where('id', $item['id'])->first();
          if ($user->tokens()) {
            $user->tokens()->delete();
          }
        }
      }
    }
    return response()->json([
      'status' => true
    ]);
  }

  public function getUserSearch()
  {
    return response()->json([
      'users' => DB::table('users')->where('is_admin', '<>', 1)->select('id', 'name')->get(),
      'message' => ''
    ]);
  }

  private function getLocation($ip): string
  {
    $currentUserInfo = Location::get($ip);
    if (!$currentUserInfo) {
      return '';
    }
    return $currentUserInfo->countryName . ', ' . $currentUserInfo->regionName . ', ' . $currentUserInfo->cityName;
  }
}
