
                                ###################################################
###################################################
# IMPORTS
###################################################
###################################################

import os
import sqlite3
import requests
import json
import re
import logging
from base64 import b64encode
import base64
from flask import Flask, request, jsonify, g, current_app
from urllib.parse import urlencode
from datetime import datetime
from hashlib import sha256
from uuid import uuid4
from dotenv import load_dotenv
from flask import Flask, g
import time
from flask import current_app
from flask import Flask, request, jsonify, g
from flask import Flask, g, current_app
from threading import Thread
from pymongo import MongoClient
from threading import Thread
from pymongo import MongoClient
from aiohttp import ClientSession, TCPConnector
from aiohttp.helpers import BasicAuth
from aiohttp.client_exceptions import ClientResponseError
import asyncio
import aiohttp
from aiohttp import web
from flask import Flask, request, jsonify, g
import threading
import aiohttp_cors

import mysql.connector

##### CREATE APPLICATION #####
app = Flask(__name__)
###################################################
###################################################
# DATABASE SETUP
###################################################
###################################################

client = MongoClient("mongodb://localhost:27017/")
db = client["labels"]
labels_collection = db["mini"]
load_dotenv()

###################################################
###################################################
# CONFIGURATION | REQUIRES .env file in same dir.
###################################################
###################################################
# EXAMPLE
###################################################
# USERS_KEY="api_key_goes_here"
# CLIENT_ID="CLIENT_ID_GOES_HERE"
# CLIENT_SECRET="CLIENT_SECRET_GOES_HERE"
# ACCOUNTNUMBER="X1Y2R3" 
# PORT=3200 
###################################################
###################################################


PORT = int(os.environ.get('PORT', 3300))

# validApiKeys = {
#     "USERNAME_KEYHOLDER": os.getenv('USERS_KEY')
# }

validApiKeys = {
    "USERNAME_KEYHOLDER": "123"
}

# acc = os.getenv('ACCOUNTNUMBER')
# clientID = os.environ.get('CLIENT_ID')
# clientSecret = os.environ.get('CLIENT_SECRET')

acc = "864835"
# clientID ="hweAmtsdYHkC6nlyO8N2q48GduP8hCQBdc6Gg9csuSbDtht6"
# clientSecret = "Yy6CZhLMdBiX4319cTdPVMGrwHxTOePEAAEOH82g14SYFTYkndVKJ4ryBa9q0iZe"

###################################################
###################################################
# API KEY VALIDATION
###################################################
###################################################

def validate_api_key(api_key):
    return api_key in validApiKeys.values()

###################################################
###################################################
# ERROR HANDLING
###################################################
###################################################

def handle_error_response(status_code, error_message):
    print(error_message)  # Assuming you want to log the error message
    return web.json_response({'error': error_message}, status=status_code)

###################################################
###################################################
# TOKEN GENERATION LOGIC
###################################################
###################################################


access_token_data = None
valid_token = False

async def generate_token():
    global access_token_data
    global valid_token

    while True:
       
        # fectch client key
        #establishing the connection
        conn = mysql.connector.connect(
        user='crushippers', password='h8maLbJPSkTyw2AJ', host='**************', database='crushippers')

        #Creating a cursor object using the cursor() method
        cursor = conn.cursor()
        #Retrieving single row
        sql = '''SELECT value from settings_teztship where order_no in (0, 1, 2) order by order_no'''

        #Executing the query
        cursor.execute(sql)

        #Fetching 1st row from the table
        result = cursor.fetchall()
       
        clientID = result[1][0]
        clientSecret = result[2][0]
        acc = result[0][0]
        
        #clientID = 'UHGyPE6ZSCUE2vqlHAiTlmBHHAVxBsqCObATvRHhirzSfASd'
        #clientSecret = 'Y2e3A6fpggA0Aajf9lFXMCv55EvmZMJDr2u9YYfqv7Abxlg3vceSVFC8IhL2xazb'
        #acc = '5Y71E4'
        
        
        cursor.close()
        
        print("Account number: ", acc)
        print("Client id: ", clientID)
        print("Client secret: ", clientSecret)

        auth_string = f"{clientID}:{clientSecret}"
        base64_auth = base64.b64encode(auth_string.encode()).decode()

        headers = {
            'Authorization': f'Basic {base64_auth}',
            'Accept': 'application/json',
            'X-Merchant-Id': clientID,
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        body = {
            'grant_type': 'client_credentials'
        }

        try:
            async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(verify_ssl=False)) as session:
                async with session.post('https://wwwcie.ups.com/security/v1/oauth/token', data=body, headers=headers) as response:
                    response.raise_for_status()
                    access_token_data = await response.json()
                    valid_token = True

                    expires_in = int(access_token_data['expires_in']) - 60  # Reduce expiration time by 60 seconds (1 minute)
                    print(f"New token in {expires_in} seconds...")
                    
                    #set to DB
                    cursor = conn.cursor()
                    sql = '''update access_key set value_ = %s where key_ = %s'''
                    data = (access_token_data['access_token'], 'teztship')
                    cursor.execute(sql, data)
                    conn.commit()
                    cursor.close()
                    
                    await asyncio.sleep(expires_in)
        except aiohttp.ClientError as error:
            valid_token = False
            print("Token generation error:", error)
            await asyncio.sleep(60)  # Wait for a minute before retrying


###################################################
###################################################
# ENSURE VALID TOKEN
###################################################
###################################################

async def ensure_valid_token():
    global access_token_data
    global valid_token
    
    if access_token_data is None or not valid_token:
        await generate_token()


###################################################
###################################################
# SANITIZE INPUT, CASE INSENSITIVE 
# NORMALIZE REQUEST BODY , levenshteinDistance
# 1) Clean Input.
# 2) Make Post Body The Same No Matter What.
# 3) Find Best Possible Match Incase Of A Mistake.

# GOAL IS TO HAVE A FORGIVING NATURE TO API REQS 
###################################################
###################################################

current_date = datetime.now().strftime('%m-%d')

def sanitize_input(input):
    if isinstance(input, str):
        return re.sub(r'[^a-zA-Z0-9\s\-@.]', '', input)
    elif isinstance(input, (int, float)):
        return input
    elif isinstance(input, bool):
        return input
    return ''


def normalizeRequestBody(req, res, next):
    def toLowerCase(obj):
        if not isinstance(obj, dict):
            return obj
        for k, v in list(obj.items()):
            if isinstance(v, dict):
                obj[k.lower()] = toLowerCase(v)
                if k.lower() != k:
                    del obj[k]
        return obj

    req.body = toLowerCase(req.body)
    next()


def case_insensitive_get(dictionary, key, default=None):
    lowercase_dict = {k.lower(): v for k, v in dictionary.items()}
    return lowercase_dict.get(key.lower(), default)


def get_service_code(speed_description):
    services = [
        { "code": "14", "description": "next day air early" },
        { "code": "13", "description": "next day air saver" },
        { "code": "01", "description": "next day air" },
        { "code": "59", "description": "2 day air a.m" },
        { "code": "02", "description": "2nd day air" },
        { "code": "12", "description": "3 day select" },
        { "code": "03", "description": "ups ground" },
    ]

    # First, try to find an exact match
    for service in services:
        if speed_description.lower() == service['description'].lower():
            return {"code": service['code']}

    # If no exact match is found, use Levenshtein distance
    min_distance = float('inf')
    closest_service = None
    for service in services:
        distance = levenshteinDistance(speed_description.lower(), service['description'].lower())
        if distance < min_distance:
            min_distance = distance
            closest_service = service

    if closest_service:
        return {"code": closest_service['code']}
    else:
        return {"error": "Service not found: " + speed_description}


def levenshteinDistance(s1, s2):
    if len(s1) > len(s2):
        s1, s2 = s2, s1

    distances = list(range(len(s1) + 1))
    for i2 in range(len(s2)):
        newDistances = [i2 + 1]
        for i1 in range(len(s1)):
            if s1[i1] == s2[i2]:
                newDistances.append(distances[i1])
            else:
                newDistances.append(1 + min(distances[i1], distances[i1 + 1], newDistances[i1]))
        distances = newDistances
    return distances[-1]



###################################################
###################################################
# ENDPOINT AND ROUTE HANDLING
###################################################
###################################################

async def ship_it(request):
    try:
        apiKey = "123" #request.headers.get('api-key', '').strip()
        if not apiKey or not validate_api_key(apiKey):
            app.logger.error('Invalid API key: %s', apiKey)
            return web.json_response({'error': 'Unauthorized'}, status=401)

        newPayload = await map_new_payload_to_original(await request.json())
        app.logger.info(json.dumps(newPayload, indent=2))
        
        await ensure_valid_token()  # Await the async function
        
        # fectch client key
        #establishing the connection
        conn = mysql.connector.connect(
        user='crushippers', password='h8maLbJPSkTyw2AJ', host='**************', database='crushippers')

        #Creating a cursor object using the cursor() method
        cursor = conn.cursor()
        #Retrieving single row
        sql = '''SELECT value_ from access_key where key_ = "teztship"'''
                    
        #Executing the query
        cursor.execute(sql)

        #Fetching 1st row from the table
        result = cursor.fetchone()
        accessToken = result[0]
    
        print("GET: ", accessToken)
        #accessToken = access_token_data.get('access_token')
        if not accessToken:
            app.logger.error('Access token not found in response')
            return web.json_response({'error': 'Internal Server Error'}, status=500)

        version = 'v1'
        query = urlencode({'additionaladdressvalidation': 'string'})

        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {accessToken}'
        }

        async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(verify_ssl=False)) as session:
            async with session.post(f'https://onlinetools.ups.com/api/shipments/{version}/ship?{query}', headers=headers, json=newPayload) as resp:
                try:
                    resp_data = await resp.json()
                    if 'response' in resp_data and 'errors' in resp_data['response']:
                        for error in resp_data['response']['errors']:
                            error_code = error.get('code')
                            error_message = error.get('message')
                            # Handle the error dynamically based on error code and message
                            print(f"Error {error_code}: {error_message}")
                            print(json.dumps(newPayload, indent=2))
                            print(f'Api Key Used: {apiKey}')
                        return web.json_response(resp_data, status=resp.status)
                except ValueError:
                    # Handle the case where the response is not valid JSON
                    print("Error: Invalid JSON response")
                    return web.json_response({'error': 'Unauthorized'}, status=resp.status)

                if resp.status != 200:
                    # If not a 200 status, print the response as-is
                    print(await resp.text())

                resp.raise_for_status()
                data = resp_data

        trackingNumber = data.get('ShipmentResponse', {}).get('ShipmentResults', {}).get('PackageResults', {}).get('TrackingNumber')
        shippingLabelImage = data.get('ShipmentResponse', {}).get('ShipmentResults', {}).get('PackageResults', {}).get('ShippingLabel', {}).get('GraphicImage')

        inserted_document = {
            'user_id': apiKey,
            'trackingNumber': trackingNumber,
            'shippingLabelImage': shippingLabelImage,
            'date': current_date  # Add the current date

        }

        try:
            result = labels_collection.insert_one(inserted_document)
            if result.inserted_id:
                print(f'Label with ID: {result.inserted_id} was created using API key: {apiKey}')
                return web.json_response({'trackingNumber': trackingNumber, 'shippingLabelImage': shippingLabelImage})
            else:
                app.logger.error('Error inserting data into MongoDB')
                return web.json_response({'error': 'Error inserting data into the database'}, status=500)
        except Exception as error:
            error_message = f'An unexpected error occurred: {error}'
            print(f'{newPayload} Api Key Used: {apiKey}')
            return handle_error_response(500, error_message)
    except Exception as error:
        error_message = f'An unexpected error occurred: {error}'
        print(f'{newPayload} Api Key Used: {apiKey}')
        return handle_error_response(500, error_message)


###################################################
###################################################
# MAP NEW PAYLOAD TO ORIGINAL FUNCTION
###################################################
###################################################

async def map_new_payload_to_original(data):

    global acc
    
    # fectch client key
    #establishing the connection
    conn = mysql.connector.connect(
    user='crushippers', password='h8maLbJPSkTyw2AJ', host='**************', database='crushippers')

    #Creating a cursor object using the cursor() method
    cursor = conn.cursor()
    #Retrieving single row
    sql = '''SELECT value from settings_teztship where order_no = 0'''

    #Executing the query
    cursor.execute(sql)

    #Fetching 1st row from the table
    result = cursor.fetchone()
    acc = result[0]
    
    print("ACC: ", acc)

    speed_description = case_insensitive_get(data, 'pkgInfo', {}).get('serviceName')
    service_code_response = get_service_code(speed_description)
    service_code = service_code_response["code"]

    from_customer = case_insensitive_get(data, 'fromCustomer', {})
    to_customer = case_insensitive_get(data, 'toCustomer', {})

    from_phone = sanitize_input(case_insensitive_get(from_customer, 'phone')) or ''
    to_phone = sanitize_input(case_insensitive_get(to_customer, 'phone')) or ''
    
    delivery_saturday = sanitize_input(case_insensitive_get(data, 'saturdayDelivery'))
    refNumber = case_insensitive_get(data, 'pkgInfo', {}).get('ref1') or ''

    send_delivery_satuday = ''
    
    if delivery_saturday == True:
        send_delivery_satuday = '1'

    if len(from_phone) < 10:
        from_phone = ''
    if len(to_phone) < 10:
        to_phone = ''

    shipment_request = {
        'ShipmentRequest': {
            'Request': {
                'SubVersion': '2205',
                'RequestOption': 'nonvalidate',
                'TransactionReference': {
                    'CustomerContext': '',
                },
            },
            'Shipment': {
                'Description': 'Shipment',
                'Shipper': {
                    'Name': sanitize_input(case_insensitive_get(from_customer, 'name')) or sanitize_input(case_insensitive_get(from_customer, 'companyName')),
                    'AttentionName': sanitize_input(case_insensitive_get(from_customer, 'companyName')) or sanitize_input(case_insensitive_get(from_customer, 'name')),
                    'TaxIdentificationNumber': '123456',
                    'Phone': {
                        'Number': from_phone,
                        'Extension': ' ',
                    },
                    'ShipperNumber': acc,
                    'FaxNumber': sanitize_input(case_insensitive_get(data, 'fromCustomer', {}).get('fax', '8002222222')),
                    'Address': {
                        'AddressLine': [
                            sanitize_input(case_insensitive_get(from_customer, 'address1')),
                            sanitize_input(case_insensitive_get(from_customer, 'address2')),
                            sanitize_input(case_insensitive_get(from_customer, 'address3')),
                        ],
                        'City': sanitize_input(case_insensitive_get(from_customer, 'city')),
                        'StateProvinceCode': sanitize_input(case_insensitive_get(from_customer, 'state')),
                        'PostalCode': sanitize_input(case_insensitive_get(from_customer, 'postalCode')),
                        'CountryCode': 'US',
                    },
                },
                'ShipTo': {
                    'Name': sanitize_input(case_insensitive_get(to_customer, 'name')) or sanitize_input(case_insensitive_get(to_customer, 'companyName')),
                    'AttentionName': sanitize_input(case_insensitive_get(to_customer, 'companyName')) or sanitize_input(case_insensitive_get(to_customer, 'name')),
                    'Phone': {
                        'Number': to_phone,
                    },
                    'Address': {
                        'AddressLine': [
                            sanitize_input(case_insensitive_get(to_customer, 'address1')),
                            sanitize_input(case_insensitive_get(to_customer, 'address2')),
                        ],
                        'City': sanitize_input(case_insensitive_get(to_customer, 'city')),
                        'StateProvinceCode': sanitize_input(case_insensitive_get(to_customer, 'state')),
                        'PostalCode': sanitize_input(case_insensitive_get(to_customer, 'postalCode')),
                        'CountryCode': 'US',
                    },
                    'Residential': ' ',
                },
                'ShipFrom': {
                    'Name': sanitize_input(case_insensitive_get(from_customer, 'name')) or sanitize_input(case_insensitive_get(from_customer, 'companyName')),
                    'AttentionName': sanitize_input(case_insensitive_get(from_customer, 'companyName')) or sanitize_input(case_insensitive_get(from_customer, 'name')),
                    'Phone': {
                        'Number': from_phone,
                    },
                    'FaxNumber': sanitize_input(case_insensitive_get(data, 'fromCustomer', {}).get('fax')),
                    'Address': {
                        'AddressLine': [
                            sanitize_input(case_insensitive_get(from_customer, 'address1')),
                            sanitize_input(case_insensitive_get(from_customer, 'address2')),
                            sanitize_input(case_insensitive_get(from_customer, 'address3')),
                        ],
                        'City': sanitize_input(case_insensitive_get(from_customer, 'city')),
                        'StateProvinceCode': sanitize_input(case_insensitive_get(from_customer, 'state')),
                        'PostalCode': sanitize_input(case_insensitive_get(from_customer, 'postalCode')),
                        'CountryCode': 'US',
                    },
                },
                'PaymentInformation': {
                    'ShipmentCharge': {
                        'Type': '01',
                        'BillShipper': {
                            'AccountNumber': acc,
                        },
                    },
                },
                'Service': {
                    'Code': sanitize_input(service_code),
                    'Description': sanitize_input(speed_description),
                },
                'ShipmentServiceOptions': {
                    'SaturdayDeliveryIndicator': send_delivery_satuday
                },
                'Package': {
                    'Description': '',
                    'ReferenceNumber': [
                        {
                            'Value': refNumber,
                        }
                        # {
                        #     'Value': case_insensitive_get(data, 'pkgInfo', {}).get('ref2') or '',
                        # }
                    ],
                    'Packaging': {
                        'Code': '02',
                        'Description': 'Misc',
                    },
                    'Dimensions': {
                        'UnitOfMeasurement': {
                            'Code': 'IN',
                            'Description': 'Inches',
                        },
                        'Length': str(sanitize_input(data['pkgInfo']['length'])),  # Use sanitize and forces string
                        'Width': str(sanitize_input(data['pkgInfo']['width'])),    # Use sanitize_and_convert_to_number
                        'Height': str(sanitize_input(data['pkgInfo']['height'])),  # Use sanitize_and_convert_to_number
                    },
                    'PackageWeight': {
                        'UnitOfMeasurement': {
                            'Code': 'LBS',
                            'Description': 'Pounds',
                        },
                        'Weight': str(sanitize_input(data['pkgInfo']['weight'])),  # Use sanitize_and_convert_to_number
                    },
                },
            },
            'LabelSpecification': {
                'LabelImageFormat': {
                    'Code': 'GIF',
                    'Description': 'GIF',
                },
                'HTTPUserAgent': 'Mozilla/4.5',
            },
        },
    }

    if (len(from_phone) <= 0):
        del shipment_request['ShipmentRequest']['Shipment']['Shipper']['Phone']
        del shipment_request['ShipmentRequest']['Shipment']['ShipFrom']['Phone']
        print (shipment_request['ShipmentRequest']['Shipment']['Shipper'])
    if (len(to_phone) <= 0):
        del shipment_request['ShipmentRequest']['Shipment']['ShipTo']['Phone']
        print (shipment_request['ShipmentRequest']['Shipment']['ShipTo'])
    
    if send_delivery_satuday == '':
        del shipment_request['ShipmentRequest']['Shipment']['ShipmentServiceOptions']

    if refNumber == '':
         del shipment_request['ShipmentRequest']['Shipment']['Package']['ReferenceNumber']
    

    return shipment_request



app = web.Application()
app.router.add_post('/create', ship_it)

if __name__ == '__main__':

    loop = asyncio.get_event_loop()
    token_task = loop.create_task(generate_token())
    token_thread = Thread(target=loop.run_until_complete, args=(token_task,))
    token_thread.daemon = True
    token_thread.start()

    cors = aiohttp_cors.setup(app, defaults={
    "*": aiohttp_cors.ResourceOptions(
            allow_credentials=True,
            expose_headers="*",
            allow_headers="*"
        )
    })

    for route in list(app.router.routes()):
        cors.add(route)

    web.run_app(app, host='0.0.0.0', port=PORT)
 
                            