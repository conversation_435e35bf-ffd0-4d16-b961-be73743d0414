APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:0N5zFrFRiWLLZdAX54i3tltV/D5nV3I8TE7TP/OW7Zs=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=teztship.com
DB_PORT=3306
DB_DATABASE=stater_kit
DB_USERNAME=root
DB_PASSWORD=Abc12345@123

DB_HOST_SHIPEASE=*********
DB_PORT_SHIPEASE=3306
DB_DATABASE_SHIPEASE=labelapi
DB_USERNAME_SHIPEASE=root
DB_PASSWORD_SHIPEASE=Abc12345@123

DB_HOST_UPS_CRAWL_ACCOUNT=*********
DB_PORT_UPS_CRAWL_ACCOUNT=3306
DB_DATABASE_UPS_CRAWL_ACCOUNT=labelapi
DB_USERNAME_UPS_CRAWL_ACCOUNT=root
DB_PASSWORD_UPS_CRAWL_ACCOUNT=Abc12345@123

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=bjjafnpstlqrttky
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

URL_API_LABEL2=http://teztship.com:7250

URL_API_LABEL_PIRREAS=https://pirreas.com/api/integration/label
API_KEY_PIRREAS=24a438fe-1afc-448c-828a-cfff00868df8

URL_API_PYTHON=http://************:3200

BATCH_ISHIP_SHUTDOWN=D:\batch_file\shutdown_iship.bat
BATCH_LABEL_API2_SHUTDOWN=D:\batch_file\shutdown_labelapi2.bat

BATCH_ISHIP_START=D:\batch_file\start_iship.bat
BATCH_LABEL_API2_START=D:\batch_file\start_labelapi2.bat

BATCH_PYTHON_CUR_START=D:\batch_file\start_python_currshipers.bat
BATCH_PYTHON_TEZTSHIP_START=D:\batch_file\start_python_teztship.bat
BATCH_PYTHON_SHIP2T_START=D:\batch_file\start_python_ship2t.bat

URL_API_LABEL2_SHIPEASE=http://shipease.biz:7280
URL_API_LABEL2_SHIP2T=http://**************:7280
URL_API_LABEL2_TEZTSHIP=http://teztship.com:7260

CLOUDINARY_CLOUD_NAME=
CLOUDINARY_API_KEY=
CLOUDINARY_API_SECRET=

TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_GROUP_ID=group-id

URL_CREATE_PLD_FILE=http://localhost:3100/create

TELEGRAM_BOT_TOKEN_BOOK_LABEL=
TELEGRAM_GROUP_ID_BOOK_LABEL=

URL_GET_PGEOCODE=http://localhost:3600/pgeocode

#1-test, 0-production
WSENV=WSURL
WSID=your_ws_id
WSKEY=your_ws_key

# usps
API_URL=https://secure.shippingapis.com/ShippingAPI.dll
API_KEY=xxx-xxx