<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use DateTime;
use Illuminate\Support\Facades\Storage;
use App\Services\UspsService;

class PriceController extends Controller
{
    private $uspsService;

    public function __construct(UspsService $uspsService)
    {
        $this->uspsService = $uspsService;
    }

    public function createCarrier(Request $request): JsonResponse
    {
        $data = $request->all();
        $file_name = '/carrier/' . uniqid() . '/' . $data['file_name'];
        Storage::disk('public')->put($file_name, base64_decode($data['logo']));

        DB::table('carrier')
        ->insert([
            'carrier_name' => $data['carrier_name'],
            'logo' => $file_name,
            'create_at' => new DateTime(('now')),
            'status' => 1
        ]);

        return response()->json([
            'status' => true
        ]);
    }

    public function getCarrier(Request $request) {
        $data = $request->all();
        $query = DB::table('carrier');
        if(isset($data['status'])) {
            $query->where('status', '=', $data['status']);
        }
        $data = $query->get();
        return response()->json([
            'carrier' => $data,
            'message' => ''
        ]);
    }

    public function deleteCarrier($id) {
        DB::table('carrier')->where('id', '=', $id)->delete();
        return response()->json([
            'status' => true
        ]);
    }

    public function updateCarrier(Request $request) {
        $data = $request->all();
        DB::table('carrier')->where('id', '=', $data['id'])
        ->update([
            'status' => $data['status']
        ]);
        return response()->json([
            'status' => true
        ]);
    }

    public function createService(Request $request)
    {
        $data = $request->all();
        if(isset($data['id'])) {
            DB::table('services')->where('id', '=', $data['id'])->update([
                'carrier_id' => $data['carrier_id'],
                'service_name' => $data['service_name'],
                'service_data_name' => $data['service_data_name'],
                'max_weight' => $data['max_weight'],
                'provider' => $data['provider'],
                'uid' => $data['uid'] ?? '',
                'discount' => $data['discount'] ?? null,
                'transit_day' => $data['transit_day'] ?? null,
                'serviceIdMap' => $data['serviceIdMap'] ?? null
            ]);
        } else {
            DB::table('services')->insert([
                'carrier_id' => $data['carrier_id'],
                'service_name' => $data['service_name'],
                'service_data_name' => $data['service_data_name'],
                'max_weight' => $data['max_weight'],
                'provider' => $data['provider'],
                'uid' => $data['uid'] ?? '',
                'discount' => $data['discount'] ?? null,
                'status' => 1,
                'is_deleted' => 0,
                'create_at' => new DateTime(('now')),
                'transit_day' => $data['transit_day'] ?? null,
                'serviceIdMap' => $data['serviceIdMap'] ?? null
            ]);
        }
        return response()->json([
            'status' => true
        ]);
    }

    public function getService(Request $request) {
        $data = $request->all();
        $query = DB::table('services')
        ->join('carrier', 'carrier.id', '=', 'services.carrier_id')
        ->where('is_deleted', '<>', 1);

        $query = $query->select(
            'carrier.logo',
            'services.*',
            'carrier_name'
        );

        $data = $query->orderBy('carrier_name')->paginate($data['itemsPerPage'] ?? 10);
        return response()->json([
            'services' => $data,
            'message' => ''
        ]);
    }

    public function deleteService(Request $request) {
        $data = $request->all();
        foreach($data['data_update'] as $item) {
            DB::table('services')->where('id', '=', $item)->update(['is_deleted' => 1]);
        }
        return response()->json([
            'status' => true
        ]);
    }

    public function updateStatusService(Request $request) {
        $data = $request->all();
        foreach($data['dataUpdate'] as $item) {
            DB::table('services')
            ->where('id', '=', $item['id'])
            ->update(['status' => $item['status']]);
        }
        return response()->json([
            'status' => true
        ]);
    }

    public function getPrice($service_id) {
        return response()->json([
            'price' => DB::table('price')->where('service_id', '=', $service_id)->get(),
            'message' => ''
        ]);
    }

    public function createPrice(Request $request): JsonResponse
    {
        $data = $request->all();

        DB::transaction(function () use ($data) {
            DB::table('price')->where('service_id', '=', $data['service_id'])->delete();
            foreach($data['price'] as $item) {
                DB::table('price')->insert([
                    'service_id' => $data['service_id'],
                    'weight_from' => $item['weight_from'],
                    'weight_to' => $item['weight_to'],
                    'price' => $item['price'],
                    'create_at' => new DateTime('now')
                ]);
            }
        });
        return response()->json([
            'status' => true
        ]);
    }

    public function getPriceForUser(Request $request): JsonResponse
    {
        $data = $request->all();
		$user_id = $request->user()->id;

        // get carrier_id from services where id = service_id
        $carrier= DB::table('carrier')->where('id', '=', $data['carrier_id'])->first();

        if ($carrier->carrier_name == 'USPS') {
            $dataPrice = $this->uspsService->getRate($data, $carrier);
        } else {
            $dataPrice = $this->getDataPriceForUser('custom_price', $data, $user_id);
        }
	    return response()->json([
		    'service' => $dataPrice,
		    'message' => ''
	    ]);
    }


	public function deletePriceCustom(Request $request): JsonResponse
	{
		$data = $request->all();
		DB::transaction(function () use ($data) {
			DB::table('user_service')
				->where('user_id', '=', $data['user_id'])
				->where('service_id', '=', $data['service_id'])
				->delete();

			DB::table('custom_price')
				->where('user_id', '=', $data['user_id'])
				->where('service_id', '=', $data['service_id'])
				->delete();
		});
		return response()->json([
			'message' => ''
		]);
	}

	private function getDataPriceForUser($table, $data, $user_id): array
	{
		$whereUser = '';
		$dataWhere = [];
		$tableJoin = 'price';
		if($table === 'custom_price') {
			$whereUser = " AND price.user_id = :user_id";
			$dataWhere['user_id'] = $user_id;
			$tableJoin = 'custom_price price';
		}
		$dataWhere['carrier_id'] = $data['carrier_id'];
			$dataWhere['weightInputFrom'] = $data['weight'];
			$dataWhere['weightInputTo'] = $data['weight'];

		$sql = "SELECT DISTINCT
                carrier.logo
                ,services.id as services_id
                ,services.service_name
                ,services.transit_day
                ,price.price
                ,price.weight_to
                ,price.weight_from
                ,'' AS transit_date
                FROM services
                INNER JOIN carrier ON carrier.id = services.carrier_id
                INNER JOIN ". $tableJoin ." ON services.id = price.service_id
                INNER JOIN user_service ON user_service.service_id = price.service_id AND user_service.is_enable = 1
                WHERE services.carrier_id = :carrier_id
                AND services.`status` = 1
                AND services.is_deleted <> 1
                AND :weightInputFrom >= price.weight_from AND :weightInputTo <= price.weight_to " . $whereUser;
		return DB::select($sql, $dataWhere);
	}

	public function getServiceByCarrier($carrier_id): JsonResponse
	{
		return response()->json([
			'service' => DB::table('services')
				->where('carrier_id', '=', $carrier_id)
				->where('is_deleted', '=', 0)
				->where('status', '=', 1)
				->get(),
			'message' => ''
		]);
	}

	public function getPriceByService(Request $request): JsonResponse
	{
		$data = $request->all();
		$price_custom = DB::table('custom_price')
			->where('service_id', '=', $data['service_id'])
			->where('user_id', '=', $data['user_id'])
			->get();
		if (count($price_custom) > 0) {
			return response()->json([
				'price' => $price_custom,
				'message' => ''
			]);
		}

		$price = DB::table('price')->where('service_id', '=', $data['service_id'])
			->get();
		return response()->json([
			'price' => $price,
			'message' => ''
		]);
	}

	public function createPriceCustom(Request $request): JsonResponse
	{
		$data = $request->all();

		DB::transaction(function () use ($data) {
			DB::table('user_service')->insertOrIgnore([
				'user_id' => $data['user_id'],
				'service_id' => $data['service_id'],
				'is_enable' => 1
			]);

			DB::table('custom_price')
				->where('service_id', '=', $data['service_id'])
				->where('user_id', '=', $data['user_id'])
				->delete();
			foreach($data['price'] as $item) {
				DB::table('custom_price')->insert([
					'id' => uniqid(),
					'user_id' => $data['user_id'],
					'service_id' => $data['service_id'],
					'weight_from' => $item['weight_from'],
					'weight_to' => $item['weight_to'],
					'price' => $item['price'],
					'create_at' => new DateTime('now')
				]);
			}
		});
		return response()->json([
			'status' => true
		]);
	}

	public function updateStatusCustomService(Request $request): JsonResponse
	{
		$data = $request->all();
		DB::table('user_service')->where('user_id', '=', $data['user_id'])
			->where('service_id', '=', $data['service_id'])
			->update([
				'is_enable' => $data['is_enable']
			]);
		return response()->json([
			'status' => 200
		]);
	}
}
