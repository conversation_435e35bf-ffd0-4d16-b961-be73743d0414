<?php

namespace App\Http\Controllers;

use App\Http\Helper\PDFHelper;
use Dompdf\Dompdf;
use Dompdf\Options;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\View;
use Intervention\Image\Facades\Image;
use Jurosh\PDFMerge\PDFMerger;
use DateTime;
use <PERSON>bauman\Location\Facades\Location;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use ZipArchive;

class PdfController extends Controller
{
    public function createPdf(Request $request): string
    {
        $json = file_get_contents(public_path('storage/temp/data.json'));
        $data = json_decode($json, true);
        $barcode_url = 'temp/' . uniqid() . '.png';

        PdfController::cropImage($data['label_image'], $barcode_url);

        // Instantiate Dompdf with options
        $dompdf = PDFHelper::createTemplateFDF();

        // HTML content for the PDF
        $view = View::make('template_pdf.order_crushippers', [
            'data' => $data,
            'barcode' => 'storage/' . $barcode_url
        ])->render();

        // Load HTML content into Dompdf
        $dompdf->loadHtml($view);

        // Output PDF as a file (optional)
        // Render PDF (output)
        $dompdf->render();

        // Save PDF to the server's filesystem
        $pdfFilePath = public_path('storage/pdfs/document.pdf'); // Change the path as needed
        file_put_contents($pdfFilePath, $dompdf->output());
        Storage::disk('public')->delete($barcode_url);
        // Optionally, you can return a response or redirect the user
        // For example, return a success message
        return 'PDF saved successfully at: ' . $pdfFilePath;
    }

    public function genPdf()
    {
        $ip = '************';
        $currentUserInfo = Location::get($ip);
        $data = json_decode('{"dataFrom":{"addressBookSelect":{"id":12,"user_id":2,"name":"HUGHES","company_name":null,"phone_number":null,"country":"US","address1":"42 Secret Garden","address2":null,"zip":"92620","city":"Irvine","state":"CA","is_default":0,"create_at":"2024-01-02 08:16:38"},"name":"TUAN","companyName":"COMPANY From","phone":null,"country":"US","address1":"414 West 14th Street","address2":"","zip":"10014","city":"New York","state":"NY","saveAddress":false,"saveAddressDefault":false},"dataTo":{"addressBookSelect":{"id":12,"user_id":2,"name":"HUGHES","company_name":null,"phone_number":null,"country":"US","address1":"42 Secret Garden","address2":null,"zip":"92620","city":"Irvine","state":"CA","is_default":0,"create_at":"2024-01-02 08:16:38"},"name":"HUGHES","companyName":"767","phone":"5675","country":"US","address1":"42 Secret Garden","address2":"5","zip":"92620","city":"Irvine","state":"CA","saveAddress":false,"saveAddressDefault":false},"package":{"packageSelected":{"id":173,"user_id":2,"package_name":"50","no_of_package":1,"weight":80,"length":20,"width":20,"height":15,"ref1":"FOOD","ref2":null,"create_at":"2024-01-22 18:59:53"},"noOfPackage":1,"weight":80,"length":20,"width":20,"height":15,"ref1":"FOOD","ref2":null,"savePackage":false,"namePackageSaved":null,"saturdayDelivery":false,"signature":null,"serviceDetail":10},"service_name":"UPS Next Day Air Early","user_id":2,"price":17,"service_id":33,"transit_date":"2024\/03\/06","routeCode":" CA 926 9-01","lblservicename":"UPS NEXT DAY AIR EARLY","label_image":"\/orders\/2\/65e7498a268fc.png","tracking_no":"1ZB414621453031478"}', true);
        $barcode_url = 'temp/' . uniqid() . '.png';

        PdfController::cropImage($data['label_image'], $barcode_url);
        return view('template_pdf.order', [
            'data' => $data,
            'barcode' => 'storage/' . $barcode_url
        ]);
    }

    public static function genPDFCrushippers($data, $url_pdf) {
        $barcode_url = 'temp/' . uniqid() . '.png';

        PdfController::cropImage($data['label_image'], $barcode_url);

        // Instantiate Dompdf with options
        $dompdf = PDFHelper::createTemplateFDF();

        // HTML content for the PDF
        $view = View::make('template_pdf.order_crushippers', [
            'data' => $data,
            'barcode' => 'storage/' . $barcode_url
        ])->render();

        // Load HTML content into Dompdf
        $dompdf->loadHtml($view);

        // Output PDF as a file (optional)
        // Render PDF (output)
        $dompdf->render();

        // Save PDF to the server's filesystem
        file_put_contents($url_pdf, $dompdf->output());

        Storage::disk('public')->delete($barcode_url);
    }

	public static function genPDFLabelApi2Shipease($data, $url_pdf) {
		$barcode_url = 'temp/' . uniqid() . '.png';

		PdfController::cropImage($data['label_image'], $barcode_url);

		// Instantiate Dompdf with options
		$dompdf = PDFHelper::createTemplateFDF();

		// HTML content for the PDF
		$view = View::make('template_pdf.order_shippease', [
			'data' => $data,
			'barcode' => 'storage/' . $barcode_url
		])->render();

		// Load HTML content into Dompdf
		$dompdf->loadHtml($view);

		// Output PDF as a file (optional)
		// Render PDF (output)
		$dompdf->render();

		// Save PDF to the server's filesystem
		file_put_contents($url_pdf, $dompdf->output());

		Storage::disk('public')->delete($barcode_url);
	}

    public static function genPDFLabelTeztship($data, $url_pdf): void
    {
        $barcode_url = 'temp/' . uniqid() . '.png';

        PdfController::cropImage($data['label_image'], $barcode_url);

        // Instantiate Dompdf with options
        $dompdf = PDFHelper::createTemplateFDF();

        // HTML content for the PDF
        $view = View::make('template_pdf.order_teztship', [
            'data' => $data,
            'barcode' => 'storage/' . $barcode_url
        ])->render();

        // Load HTML content into Dompdf
        $dompdf->loadHtml($view);

        // Output PDF as a file (optional)
        // Render PDF (output)
        $dompdf->render();

        // Save PDF to the server's filesystem
        file_put_contents($url_pdf, $dompdf->output());

        Storage::disk('public')->delete($barcode_url);
    }

    public static function createPDFV2($image_file, $pdf_file): string
    {
        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $options->set('margin_top', 0);
        $options->set('margin_bottom', 0);
        $options->set('margin_left', 0);
        $options->set('margin_right', 0);
        $options->setIsRemoteEnabled(true);

        $dompdf = new Dompdf($options);

        $dompdf->set_option('tempDir', storage_path('app/tmp'));

        $dompdf->setPaper(array(0, 0, 270, 405), 'portrait');

        $data = ['image_url' => $image_file];

        // HTML content for the PDF
        $view = View::make('template_pdf.order2', $data)->render();

        // Load HTML content into Dompdf
        $dompdf->loadHtml($view);

        // Output PDF as a file (optional)
        // Render PDF (output)
        $dompdf->render();

        // Save PDF to the server's filesystem
        file_put_contents($pdf_file, $dompdf->output());

        // Optionally, you can return a response or redirect the user
        // For example, return a success message
        return true;
    }

    public static function createPDFV3($image_file, $pdf_file): string
    {
        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $options->set('margin_top', 0);
        $options->set('margin_bottom', 0);
        $options->set('margin_left', 0);
        $options->set('margin_right', 0);
        $options->setIsRemoteEnabled(true);

        $dompdf = new Dompdf($options);

        $dompdf->set_option('tempDir', storage_path('app/tmp'));

        $dompdf->setPaper(array(0, 0, 270, 475), 'portrait');

        $data = ['image_url' => $image_file];

        // HTML content for the PDF
        $view = View::make('template_pdf.order2', $data)->render();

        // Load HTML content into Dompdf
        $dompdf->loadHtml($view);

        // Output PDF as a file (optional)
        // Render PDF (output)
        $dompdf->render();

        // Save PDF to the server's filesystem
        file_put_contents($pdf_file, $dompdf->output());

        // Optionally, you can return a response or redirect the user
        // For example, return a success message
        return true;
    }

    public function getmutiplePdf(Request $request): BinaryFileResponse
    {
        $data = $request->all();
        $pdf = new PDFMerger;
        foreach ($data['pdfs'] as $item) {
            $pdf->addPDF(public_path('storage' . $item), 'all');
        }

        $file_name = date_format(new DateTime(('now')), 'Ymdhms');
        $path_file_merged = public_path("storage/pdfs/" . $file_name . ".pdf");
        $pdf->merge('file', $path_file_merged);

        return response()->download($path_file_merged, $file_name . '.pdf', [
            'Content-Type' => 'application/pdf',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0',
            'Pragma' => 'public',
        ])->deleteFileAfterSend(true);
    }

    public function getZipPdf(Request $request): BinaryFileResponse
    {
        // Generate a unique filename for the zip file
        $zipFileName = 'files_' . time() . '.zip';

        // Path where the zip file will be stored
        $zipFilePath = storage_path('app/public/' . $zipFileName);

        $data = $request->all();

        // Initialize a new ZipArchive instance
        $zip = new ZipArchive;

        if ($zip->open($zipFilePath, ZipArchive::CREATE) === TRUE) {
            foreach ($data['pdfs'] as $item) {
                $relativeName = basename($item);
                $zip->addFile(public_path('storage' . $item), $relativeName);
            }
            $zip->close();
        }

        return response()->download($zipFilePath)->deleteFileAfterSend(true);
    }

    public static function cropImage($label_url, $barcode_url): void
    {
        $barcode_url = Storage::disk('public')->path($barcode_url);
        $x = 10;
        $y = 380;
        $width = 220;
        $height = 215;

        $image = Storage::disk('public')->path($label_url);

        $img = Image::make($image);
        $croppedImage = $img->crop($width, $height, $x, $y);
        $croppedImage->save($barcode_url);
    }
}
