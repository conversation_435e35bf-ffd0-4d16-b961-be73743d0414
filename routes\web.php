<?php

use App\Http\Controllers\FindAccountWithPostalCode;
use App\Http\Controllers\PLDController;
use App\Http\Controllers\ResizeLabelController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

Route::get('/resize', function () {
    return view('resizePdf');
});
Route::post('/resize-pdf', [ResizeLabelController::class, 'uploadTest'])->name('resize.pdf');

Route::get('/find_account', function () {
    return view('findAccountWithPostalCode');
});
Route::post('/account/search', [FindAccountWithPostalCode::class, 'findAccount'])->name('account.search');

Route::get('/crawl_pld_account', function () {
    return view('crawl_pld_account');
});
Route::post('/account_pld/search', [PLDController::class, 'findAccount'])->name('account_pld.search');
