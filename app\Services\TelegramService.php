<?php

namespace App\Services;

use Exception;
use Telegram\Bot\Api;
use Telegram\Bot\Exceptions\TelegramSDKException;

class TelegramService
{
    protected $bots;

    public function __construct()
    {
        $this->bots = config('telegram.bots');
    }

    /**
     * @throws TelegramSDKException
     * @throws Exception
     */
    public function getBot($botName): Api
    {
        if (array_key_exists($botName, $this->bots)) {
            return new Api($this->bots[$botName]['token']);
        }

        throw new Exception("Bot {$botName} not found.");
    }
}
