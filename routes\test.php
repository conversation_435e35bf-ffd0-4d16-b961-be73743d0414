<?php

use Illuminate\Support\Facades\Route;
use App\Services\UspsService;

Route::get('/test-usps-label', function () {
    $uspsService = new UspsService();
    
    // Dữ liệu test
    $data = [
        'dataFrom' => [
            'name' => 'KC EXPRESS LLC',
            'companyName' => 'KC EXPRESS LLC',
            'address1' => '2624 TOY LN',
            'address2' => '',
            'city' => 'SAN JOSE',
            'state' => 'CA',
            'zip' => '95121',
            'phone' => '(*************'
        ],
        'dataTo' => [
            'name' => 'ARJAY C LAWSON',
            'companyName' => '',
            'address1' => '4433 SAN ERNESTO AVE',
            'address2' => 'UNIT 221B',
            'city' => 'ANCHORAGE',
            'state' => 'AK',
            'zip' => '99508-2852',
            'phone' => '(*************'
        ],
        'package' => [
            'weight' => '32.00'
        ],
        'user_id' => 'web_test'
    ];

    $service_info = (object) [
        'name' => 'USPS Ground Advantage',
        'provider' => 'usps_template'
    ];

    try {
        $result = $uspsService->createLabel($data, $service_info);
        
        return response()->json([
            'success' => true,
            'tracking_number' => $result['tracking_no'],
            'file_path' => $result['file_name'],
            'download_url' => url('storage' . $result['file_name']),
            'message' => 'USPS label created successfully!'
        ]);
        
    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});

Route::get('/test-usps-template', function () {
    $labelData = [
        'from' => [
            'name' => 'KC EXPRESS LLC',
            'company' => 'KC EXPRESS LLC',
            'address1' => '2624 TOY LN',
            'address2' => '',
            'city' => 'SAN JOSE',
            'state' => 'CA',
            'zip' => '95121',
            'phone' => '(*************'
        ],
        'to' => [
            'name' => 'ARJAY C LAWSON',
            'company' => '',
            'address1' => '4433 SAN ERNESTO AVE',
            'address2' => 'UNIT 221B',
            'city' => 'ANCHORAGE',
            'state' => 'AK',
            'zip' => '99508-2852',
            'phone' => '(*************'
        ],
        'tracking_number' => '9434 610571 1026 6386 92',
        'weight' => '32.00',
        'service_name' => 'USPS Ground Advantage'
    ];

    return view('template_pdf.usps_label', ['data' => $labelData]);
});
