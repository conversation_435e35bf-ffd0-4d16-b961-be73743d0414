<!DOCTYPE html>
<html>
<head>
    <title>From {{ $data['dataFrom']['name'] }} To {{ $data['dataTo']['name'] }}</title>
    <style>
        html {
            margin: 0.5px;
        }
        body {
            padding: 0;
            border-bottom: 1.5px solid #000; /* Add border around the content area */
            border-right: 1.5px solid #000; /* Add border around the content area */
            font-family: AvrileSans-Condensed;
            margin: 0 -1px -1px;
        }
        /* Other CSS styles for your content */

        @font-face {
            font-family: 'AvrileSans-Condensed';
            src: url("{{ secure_asset('css/AvrileSans-Condensed.ttf') }}") format('truetype');
        }
        @font-face {
            font-family: 'Avrile Sans Condensed Bold';
            src: url("{{ secure_asset('css/AvrileSans-CondensedBold.ttf') }}") format('truetype');
        }
    </style>

</head>
<body>
    <div style="position: absolute; top: 1px; left: 8px; line-height: 0.7em; font-size: 10px; text-transform: uppercase;">
        {{ $data['dataFrom']['name'] }}
        @if(isset($data['dataFrom']['phone']) && $data['dataFrom']['phone'] != '')
            <br>
            {{ $data['dataFrom']['phone'] }}
        @endif
        @if(isset($data['dataFrom']['companyName']) && $data['dataFrom']['companyName'] != '')
            <br>
            {{ $data['dataFrom']['companyName'] }}
        @endif
        @if(isset($data['dataFrom']['address2']) && $data['dataFrom']['address2'] != '')
            <br>
            {{ $data['dataFrom']['address2'] }}
        @endif
        <br>
        {{ $data['dataFrom']['address1'] }}
        <br>
        {{ $data['dataFrom']['city'] }} {{ $data['dataFrom']['state'] }} {{ $data['dataFrom']['zip'] }}
    </div>

    <div style="position: absolute; top: -11px; right: 140px; font-size: 18px; font-family: Avrile Sans Condensed Bold">
       <span>{{ $data['package']['weight'] }} LBS</span>
    </div>
    <div style="position: absolute; top: -11px; right: 18px; font-size: 18px; font-family: Avrile Sans Condensed Bold">
        1 OF 1
    </div>
    <div style="position: absolute; top: 12px; right: 80px; font-size: 13px;">
        DWT: {{ $data['package']['length'] }}, {{ $data['package']['width'] }}, {{ $data['package']['height'] }}
    </div>

    <div style="position: absolute; top: 60px; left: 8px; font-family: Avrile Sans Condensed Bold">
        <span style="font-size: 20px">SHIP TO:</span> <br>
    </div>

    <div style="position: absolute; top: 88px; left: 25px; line-height: 0.7em; font-size: 14px; text-transform: uppercase;">
        {{ $data['dataTo']['name'] }}
        @if(isset($data['dataTo']['phone']) && $data['dataTo']['phone'] != '')
            <br>
            {{ $data['dataTo']['phone'] }}
        @endif

        @if(isset($data['dataTo']['companyName']) && $data['dataTo']['companyName'] != '')
            <br>
            {{ $data['dataTo']['companyName'] }}
        @endif
        @if(isset($data['dataTo']['address2']) && $data['dataTo']['address2'] != '')
            <br>
            {{ $data['dataTo']['address2'] }}
        @endif
        <br>
        {{ $data['dataTo']['address1'] }}
        <br>
        <div style="height: 3px"></div>
        <span style="font-family: Avrile Sans Condensed Bold; font-size: 22px">
            {{ $data['dataTo']['city'] }} {{ $data['dataTo']['state'] }} {{ $data['dataTo']['zip'] }}
        </span>
    </div>



    <div style="border-top: 2.3px solid #000; height: 117px; position: absolute; top: 202px; width: 110%; margin: 0 -1px">
        @if($data['package']['weight'] >= 70)
            <div style="width: 120px; text-align: center; height: 113px">
                <div style="margin-top: -97px;">
                    <span style="font-size: 140px; font-family: Avrile Sans Condensed Bold">H</span>
                </div>
            </div>
            <div style="position: absolute; width: 4px; height: 113px; border-right: 2px solid #000; left: 115px; top: -2px"></div>
        @else
            <div style="width: 120px; border-right: 2px solid #000">
                <img src="{{ secure_asset($barcode) }}" style="margin-left: 5px; margin-top: 1px; height: 94%" alt="maxicode">
            </div>
        @endif

        <div style="position: absolute; top: -21px; left: 130px">
           <span style="font-size: 40px; font-family: Avrile Sans Condensed Bold">{{ $data['routeCode'] }}</span>
            <div style="margin-top: -4px; margin-left: 10px">{!! DNS1D::getBarcodeHTML('420' . $data['dataTo']['zip'], "C128",1.5,53) !!}</div>
        </div>
    </div>
    <div style="position: absolute; top: 315px; height: 6px; background: #000; width: 110%; margin: 0 -1px"></div>

    <div style="position: absolute; top: 315px; height: 60px; width: 110%; margin: 0 -1px; border-bottom: 2.3px solid #000; ">
        <div style="position: absolute; margin-top: -8px; font-size: 26px; margin-left: 6px; font-family: Avrile Sans Condensed Bold">
            <span style="text-transform: uppercase;">{{ $data['lblservicename'] }}</span>
        </div>
        <div style="position: absolute; margin-top: 30px; font-size: 15px; margin-left: 6px">
            <span>TRACKING #:
                {{ substr($data['tracking_no'], 0, 2) }}
                {{ substr($data['tracking_no'], 2, 3) }}
                {{ substr($data['tracking_no'], 5, 3) }}
                {{ substr($data['tracking_no'], 8, 2) }}
                {{ substr($data['tracking_no'], 10, 4) }}
                {{ substr($data['tracking_no'], 14, 4) }}
                {{ substr($data['tracking_no'], 18) }}
            </span>
        </div>

        <div style="position: absolute; right: 50px; top: -19px; font-size: 45px; font-family: Avrile Sans Condensed Bold">
            <span>
                @if(strtolower($data['lblservicename']) == 'next day air' || strtolower($data['lblservicename']) == 'ups next day air')
                    @if( $data['package']['saturdayDelivery'])
                        1S
                    @else
                        1
                    @endif
                @endif
                @if(strtolower($data['lblservicename']) == 'ups next day air early')
                    @if( $data['package']['saturdayDelivery'])
                        1+S
                    @else
                        1+
                    @endif
                @endif
            </span>
        </div>
    </div>

    <div style="position: absolute; top: 380px; height: 112px; width: 110%; margin: 0 -1px;">
        <div style="margin-top: 5px; margin-left: 25px">{!! DNS1D::getBarcodeHTML($data['tracking_no'], "C128",1.5,100) !!}</div>
    </div>
    <div style="position: absolute; top: 493px; height: 6px; background: #000; width: 110%; margin: 0 -1px"></div>
    <div style="position: absolute; top: 495px; width: 110%">
        <div style="margin-left: 3px; font-size: 11px;">BILLING: P/P</div>
    </div>

    <div style="position: absolute; top: 548px; width: 50%">
        @if($data['package']['ref1'])
            <div style="margin-left: 3px; font-size: 11px;">Reference No.1: {{ $data['package']['ref1'] }}</div>
        @endif
    </div>
    <div style="position: absolute; top: 562px; left: 28%; width: 100%; font-size: 8px">
        <div style="position: absolute;">XOL 24.04.04</div>
        <div style="position: absolute; left: 80px;">NV45 15.0A 04/2024*</div>
    </div>

    <div style="position: absolute; top: 540px; right: 55px;">
        <img src="{{ secure_asset('storage/template/gift.svg') }}" style="height: 30px" alt="gift">
    </div>
</body>
</html>
