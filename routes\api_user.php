<?php

use App\Http\Controllers\BookLabelController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'label'], function () {
    Route::get('initdata', [BookLabelController::class, 'initCreateLabel']);
    Route::post('find_service', [BookLabelController::class, 'findServiceDetail']);
});

Route::group(['prefix' => 'ontrac', 'middleware' => 'auth:sanctum'], function () {
    Route::post('bookLabel', [App\Http\Controllers\Label\Ontrac\BookLabelController::class, 'bookLabel']);
    Route::post('getRateLabelOntrac', [App\Http\Controllers\Label\Ontrac\BookLabelController::class, 'getRateLabelOntrac']);
});
