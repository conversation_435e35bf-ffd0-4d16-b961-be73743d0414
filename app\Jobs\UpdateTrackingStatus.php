<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\Order;
use Illuminate\Support\Facades\DB;

class UpdateTrackingStatus implements ShouldQueue
{
    use Dispatchable, Queueable;

    public function handle(): void
    {
        $orders = Order::whereNotNull('tracking_id')
            ->where('tracking_status', '!=', 'delivered')
            ->where('service_id', '=', 0)
            ->get()
            ->unique('tracking_id');

        // Nhóm các order theo tracking_number thành nhóm 10 cái
        $ordersGrouped = $orders->chunk(10);

        foreach ($ordersGrouped as $orderChunk) {
            $trackingNumbers = $orderChunk
                ->pluck('tracking_id')
                ->filter(fn($id) => !empty($id) && strlen($id) >= 8)
                ->implode('||');

            if (empty($trackingNumbers)) {
                continue; // Bỏ qua nếu không có tracking hợp lệ
            }

            $url = "https://ws.ontrac.com/Method/Track/v3/json/"
                . env('WSID') . "/"
                . env('WSKEY') . "/"
                . $trackingNumbers;

            // $response = Http::get($url);
            $response = Http::withOptions(['verify' => false])->get($url);

            if (!$response->successful()) {
                Log::warning("Failed to fetch tracking data for batch: {$trackingNumbers}");
                continue;
            }

            $data = $response->json();

            if (!isset($data['Track']) || !is_array($data['Track'])) {
                Log::warning("Invalid tracking response for batch: {$trackingNumbers}");
                continue;
            }

            foreach ($data['Track'] as $trackData) {
                // Lấy tracking barcode từ Pieces[0]
                $tracking = null;
                if (!empty($trackData['Pieces']) && !empty($trackData['Pieces'][0]['Barcode'])) {
                    $tracking = $trackData['Pieces'][0]['Barcode'];
                }

                if (!$tracking) {
                    continue; // Không có tracking number hợp lệ
                }

                $latestEvent = collect($trackData['Events'])->sortByDesc('UTCEventDateTime')->first();
                $eventType = $latestEvent['EventType'] ?? null;

                $newStatus = $this->map($eventType);

                if ($newStatus) {
                    DB::table('orders')
                        ->where('tracking_id', $tracking)
                        ->update(['tracking_status' => $newStatus]);

                    Log::info("Updated order with tracking {$tracking} to status {$newStatus}");
                }
            }
        }
    }

    public function map(string $eventType): ?string
    {
        return match ($eventType) {
            'OrderCreated' => 'Pre_transit',
            'Received', 'Loaded' => 'In_transit',
            'Departed' => 'Out_for_delivery',
            'Delivered' => 'delivered',
            'Miscellaneous' => 'delivered',
            default => null,
        };
    }
}
