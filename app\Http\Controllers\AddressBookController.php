<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use DateTime;

class AddressBookController extends Controller
{
    public function getListAddressBook(Request $request): JsonResponse
    {
	    $query_param = $request->all();
	    $query = DB::table('address_book');
	    $userID = $request->user()->id;
	
	    if (isset($query_param['name'])) {
		    $query->where('name', '=', $query_param['name']);
	    }
	    $query->where('user_id', '=', $userID);
	    if(isset($query_param['q'])) {
		    $query->where(function ($q) use ($query_param) {
			    $q->where("name", 'like', '%' . $query_param["q"] . '%')
				    ->orWhere("address1", 'like', '%' . $query_param["q"] . '%');
		    });
	    }
	
	    if (isset($query_param['companyName'])) {
		    $query->where('company_name', '=', $query_param['companyName']);
	    }
	
	    $query->orderByDesc('create_at');
	
	    $data_address = $query->paginate($query_param['itemsPerPage'] ?? 10);
	    return response()->json([
		    'address_book' => $data_address,
		    'message' => ''
	    ]);
    }
	
	/**
	 * deleteAddressBook
	 * @param $id
	 * @return JsonResponse
	 */
	public function deleteAddressBook($id): JsonResponse
	{
		DB::table('address_book')->where('id', '=', $id)->delete();
		return response()->json([
			'status' => true
		]);
	}
	
	/**
	 * updateAddressBook
	 * @param Request $request
	 * @return JsonResponse
	 * @throws \Exception
	 */
	public function updateAddressBook(Request $request): JsonResponse
	{
		$data = $request->all();
		$userID = $request->user()->id;
		$dataUpdate = [
			'user_id' => $userID,
			'name' => $data['name'],
			'company_name' => $data['company_name'],
			'phone_number' => $data['phone_number'],
			'country' => $data['country'],
			'address1' => $data['address1'],
			'address2' => $data['address2'],
			'zip' => $data['zip'],
			'city' => $data['city'],
			'state' => $data['state'],
			'is_default' => 0,
			'create_at' =>  new DateTime(('now'))
		];
		if(isset($data['id'])) {
			DB::table('address_book')->where('id', '=', $data['id'])
				->update($dataUpdate);
		} else {
			DB::table('address_book')->insert($dataUpdate);
		}
		return response()->json([
			'status' => true
		]);
	}
}
