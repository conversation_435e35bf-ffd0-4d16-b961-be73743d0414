<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Telegram\Bot\Laravel\Facades\Telegram;

class CheckStatusHeader extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:check-status-header';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dataHeader = DB::table('header_ups')->get();
        Log::info("Total header: ". count($dataHeader));
        foreach ($dataHeader as $key => $header) {
            Log::info('Check header: '. $header->header_name);

            $headerText = $header->headers;
            $headerLines = explode("\n", $headerText);
            $headers = [];
            foreach ($headerLines as $line) {
                list($key, $value) = explode(':', $line, 2);
                $headers[$key] = $value;
            }

            Log::info('Header Array: ', $headers);
            try {
                $response = Http::withHeaders($headers)->post('https://webapis.ups.com/doapp/api/Registration/ReadExistingAccount',
                    'R46062'
                );
                Log::info('Response ', [$response]);
                if ($response->ok()) {
                    Log::info('Response body ', [$response]);
                    $dataBody = json_decode($response->body());
                    if (isset($dataBody->Object->DoappResponse->ErrorCode) && $dataBody->Object->DoappResponse->ErrorCode === "250002") {
                        if ($header->is_send == 0) {
                            $message = "Header name: " . $header->header_name . ' error' . PHP_EOL . "Invalid Authentication Information.";
                            self::sendTelegram($message);
                        }
                        DB::table('header_ups')->where('headers_id', $header->headers_id)->update([
                            'status' => 'Not Live',
                            'is_send' => 1,
                        ]);
                    } else {
                        DB::table('header_ups')->where('headers_id', $header->headers_id)->update([
                            'status' => 'Live',
                            'is_send' => 0
                        ]);
                        $this->updateActionProcessLive($header->headers_id);
                    }
                } else {
                    if ($header->is_send == 0) {
                        $message = "Header name: " . $header->header_name . ' error' . PHP_EOL . "Invalid Authentication Information.";
                        self::sendTelegram($message);
                    }
                    DB::table('header_ups')->where('headers_id', $header->headers_id)->update([
                        'status' => 'Not Live',
                        'is_send' => 1,
                    ]);
                    $this->updateActionProcessFailed($header->headers_id);
                }
            } catch (\Exception $ex) {
                Log::error('Error ', [$ex->getMessage()]);
                if ($header->is_send == 0) {
                    $message = "Header name: " . $header->header_name . ' error' . PHP_EOL . $ex->getMessage();
                    self::sendTelegram($message);
                }
                DB::table('header_ups')->where('headers_id', $header->headers_id)->update([
                    'status' => 'Not Live',
                    'is_send' => 1,
                ]);
                $this->updateActionProcessFailed($header->headers_id);
            } finally {
                Log::info('finally '. $header->header_name);
            }
        }
    }

    private function updateActionProcessFailed(int $header_id): void
    {
        DB::table('work_account_process')
            ->where('header_id', $header_id)
            ->update([
                'action' => 'stop',
            ]);
    }

    private function updateActionProcessLive(int $header_id): void
    {
        DB::table('work_account_process')
            ->where('header_id', $header_id)
            ->where('is_failed', 0)
            ->where('action', '!=', 'run')
            ->update([
                'action' => 'run',
                'is_failed' => 1,
            ]);
    }

    static function sendTelegram($msg): void
    {
        Telegram::sendMessage([
            'chat_id' => env('TELEGRAM_GROUP_ID'),
            'parse_mode' => 'HTML',
            'text' => $msg
        ]);
    }
}
