<?php

use App\Http\Controllers\AddressBook2TController;
use App\Http\Controllers\BulkOrderController;
use App\Http\Controllers\DashBoardController;
use App\Http\Controllers\FAQsController;
use App\Http\Controllers\LabelShip2TController;
use App\Http\Controllers\LabelShipeaseController;
use App\Http\Controllers\LabelTeztShipController;
use App\Http\Controllers\PdfController;
use App\Http\Controllers\PLDController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\SettingShip2TController;
use App\Http\Controllers\TicketController;
use App\Http\Controllers\UPSCheckerController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\LabelController;
use App\Http\Controllers\PriceController;
use App\Http\Controllers\AddressBookController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::get('/check', function () {
	return response()->json(['message' => 'Welcome to the API!']);
});

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::group(['prefix' => 'auth'], function () {
    Route::post('login', [AuthController::class, 'login']);
    Route::post('register', [AuthController::class, 'register']);
	Route::post('verify', [AuthController::class, 'verifyAccount']);
	Route::post('resend', [AuthController::class, 'resendCodeVerify']);
	Route::post('admin/login', [AuthController::class, 'loginAdmin']);
	Route::post('forgotPassword', [AuthController::class, 'forgotPassword']);
	Route::post('resetPassword', [AuthController::class, 'resetPassword']);

    Route::group(['middleware' => 'auth:sanctum'], function() {
      Route::get('logout', [AuthController::class, 'logout']);
      Route::get('user', [AuthController::class, 'user']);
    });
});

Route::group(['prefix' => 'user', 'middleware' => 'auth:sanctum'], function () {
    Route::get('list', [AuthController::class, 'userList']);
    Route::post('update/{id}', [AuthController::class, 'userUpdate']);
    Route::post('delete', [AuthController::class, 'userDelete']);
    Route::post('updateStatus', [AuthController::class, 'updateStatusAllUser']);
    Route::get('usersearch', [AuthController::class, 'getUserSearch']);
});

Route::group(['prefix' => 'invoice', 'middleware' => 'auth:sanctum'], function () {
    Route::post('charge', [InvoiceController::class, 'chargeMoney']);
    Route::get('list', [InvoiceController::class, 'getInvoice']);
    Route::post('delete', [InvoiceController::class, 'deleteInvoice']);
    Route::post('update', [InvoiceController::class, 'updateStatusInvoice']);
});

Route::group(['prefix' => 'carrier', 'middleware' => 'auth:sanctum'], function () {
    Route::post('create', [PriceController::class, 'createCarrier']);
    Route::get('list', [PriceController::class, 'getCarrier']);
    Route::post('delete/{id}', [PriceController::class, 'deleteCarrier']);
    Route::post('update', [PriceController::class, 'updateCarrier']);
});

Route::group(['prefix' => 'service', 'middleware' => 'auth:sanctum'], function () {
    Route::post('create', [PriceController::class, 'createService']);
    Route::get('list', [PriceController::class, 'getService']);
    Route::post('updateStatus', [PriceController::class, 'updateStatusService']);
    Route::post('delete', [PriceController::class, 'deleteService']);
});

Route::group(['prefix' => 'price', 'middleware' => 'auth:sanctum'], function () {
    Route::post('create', [PriceController::class, 'createPrice']);
    Route::get('list/{service_id}', [PriceController::class, 'getPrice']);
});

Route::group(['prefix' => 'admin', 'middleware' => 'auth:sanctum'], function () {
    Route::post('dashboard', [DashBoardController::class, 'getDataDashboard']);
	Route::get('history/list', [LabelController::class, 'getListOrderAdmin']);
	Route::get('order/detail/{order_id}', [LabelController::class, 'getOrderDetail']);
	Route::get('refund/list', [LabelController::class, 'getRefund']);
	Route::post('refund/change', [LabelController::class, 'updateStatusRefund']);
    Route::post('pld/create', [PLDController::class, 'createFilePLD']);
    Route::get('pld/list', [PLDController::class, 'getPldList']);
    Route::post('pld/delete', [PLDController::class, 'deletePld']);

	Route::get('pricecustom', [LabelController::class, 'getDataPrice']);
	Route::post('deletepricecustom', [PriceController::class, 'deletePriceCustom']);
	Route::post('updateStatusCustomService', [PriceController::class, 'updateStatusCustomService']);

	Route::group(['prefix' => 'service'], function () {
		Route::get('search', [LabelController::class, 'getListServiceSearch']);
		Route::get('getbycarrier/{carrier_id}', [PriceController::class, 'getServiceByCarrier']);
		Route::post('price/getPriceByService', [PriceController::class, 'getPriceByService']);
		Route::post('price/createcustomprice', [PriceController::class, 'createPriceCustom']);
	});

	Route::group(['prefix' => 'faqs'], function () {
		Route::get('list', [FAQsController::class, 'getFAQs']);
		Route::get('user', [FAQsController::class, 'getFAQsUser']);
		Route::post('delete/{id}', [FAQsController::class, 'deleteFAQs']);
		Route::post('update', [FAQsController::class, 'updateFAQs']);
		Route::post('updateStatus', [FAQsController::class, 'updateStatus']);
	});

	Route::group(['prefix' => 'ticket'], function () {
		Route::post('create', [TicketController::class, 'createTicket']);
		Route::get('list', [TicketController::class, 'getListTicketForAdmin']);
		Route::get('detail', [TicketController::class, 'getTicketDetail']);
		Route::post('updateStatus', [TicketController::class, 'updateStatus']);
	});

	Route::group(['prefix' => 'settings'], function () {
		Route::post('create', [SettingController::class, 'updateSettings']);
		Route::get('list', [SettingController::class, 'getListSetting']);
        Route::post('shutdown', [SettingController::class, 'shutDownServiceApi']);
        Route::post('restart', [SettingController::class, 'restartServiceApi']);
        Route::post('start', [SettingController::class, 'startServiceApi']);
        Route::get('getListAccountNo', [SettingController::class, 'getListAccountNo']);
		Route::post('updateListAccountNo', [SettingController::class, 'updateListAccountNo']);

		Route::get('getListAccountNoShipease', [SettingController::class, 'getListAccountNoForShipease']);
		Route::post('updateListAccountNoShipease', [SettingController::class, 'updateListAccountNoForShipease']);

        Route::group(['prefix' => 'ship2t'], function () {
            Route::get('list', [SettingShip2TController::class, 'getListSetting']);
            Route::post('update', [SettingShip2TController::class, 'updateSettings']);
            Route::post('updateAccount', [SettingShip2TController::class, 'updateListAccountNoForShip2t']);
        });
	});
});

Route::group(['prefix' => 'user', 'middleware' => 'auth:sanctum'], function () {
    Route::post('dashboard', [DashBoardController::class, 'getDashBoardUser']);
    Route::post('service/list', [PriceController::class, 'getPriceForUser']);
	Route::get('history/list', [LabelController::class, 'getListOrder']);
	Route::get('service/search', [LabelController::class, 'getListServiceSearch']);
	Route::get('invoice/list', [InvoiceController::class, 'getInvoice']);
	Route::post('invoice/delete', [InvoiceController::class, 'deleteInvoice']);
	Route::get('transaction/list', [LabelController::class, 'getListTransaction']);
	Route::post('label/create', [LabelController::class, 'createLabel2']);
	Route::get('label/initdata', [LabelController::class, 'initCreateLabel']);
	Route::get('label/getListPackage', [LabelController::class, 'getListPackage']);
	Route::post('label/deletePackage/{id}', [LabelController::class, 'deletePackage']);
	Route::post('label/updatePackage', [LabelController::class, 'updatePackage']);
	Route::get('addressBook/getListAddressBook', [AddressBookController::class, 'getListAddressBook']);
	Route::post('addressBook/deleteAddressBook/{id}', [AddressBookController::class, 'deleteAddressBook']);
	Route::post('addressBook/updateAddressBook', [AddressBookController::class, 'updateAddressBook']);
	Route::get('price', [LabelController::class, 'getDataPrice']);
	Route::get('order/detail/{order_id}', [LabelController::class, 'getOrderDetail']);
	Route::post('label/duplicate', [LabelController::class, 'duplicateLabel']);
	Route::post('refund/create', [LabelController::class, 'addRefund']);
	Route::get('carrier/list', [PriceController::class, 'getCarrier']);

	Route::group(['prefix' => 'bulkorder'], function () {
		Route::post('validate', [BulkOrderController::class, 'validateCsv']);
		Route::post('create', [BulkOrderController::class, 'createBulkOrder']);
		Route::get('template', [BulkOrderController::class, 'downloadTemplate']);
	});

	Route::group(['prefix' => 'faqs'], function () {
		Route::get('list', [FAQsController::class, 'getFAQsUser']);
	});

	Route::group(['prefix' => 'ticket'], function () {
		Route::post('create', [TicketController::class, 'createTicket']);
		Route::get('list', [TicketController::class, 'getListTicketForUser']);
		Route::get('detail', [TicketController::class, 'getTicketDetail']);
		Route::post('updateStatus', [TicketController::class, 'updateStatus']);
	});

	Route::group(['prefix' => 'settings'], function () {
		Route::post('create', [SettingController::class, 'updateSettings']);
		Route::get('list', [SettingController::class, 'getListSetting']);
	});



});

Route::post('getmutiplePdf', [PdfController::class, 'getmutiplePdf']);
Route::post('getZipPdf', [PdfController::class, 'getZipPdf']);


Route::post('createPdf', [PdfController::class, 'createPdf']);
Route::post('createPDFV2', [PdfController::class, 'createPDFV2']);
Route::get('genPdf', [PdfController::class, 'genPdf']);

Route::post('shutdown', [SettingController::class, 'shutDownService']);
Route::post('restart', [SettingController::class, 'restartService']);
Route::post('start', [SettingController::class, 'startService']);


Route::post('getLabelShipease', [LabelShipeaseController::class, 'getLabelShipease']);
Route::get('/orders', [LabelShipeaseController::class, 'getOrder']);

Route::apiResource('addressbookShip2T', AddressBook2TController::class);
Route::get('ordersShip2T', [LabelShip2TController::class, 'getOrder']);
Route::post('getLabelShip2t', [LabelShip2TController::class, 'getLabelShip2T']);
Route::post('saveOrderShip2t', [LabelShip2TController::class, 'saveOrder']);
Route::post('book-label', [LabelShip2TController::class, 'bookLabel']);
Route::get('tracker-user', [LabelShip2TController::class, 'getTrackerUser']);

Route::post('getLabelTeztship', [LabelTeztShipController::class, 'getLabelTeztship']);

Route::post('resizeLabel', [LabelTeztShipController::class, 'getLabelTeztship']);

Route::group(['prefix' => 'checkerups'], function () {
    Route::post('addtask', [UPSCheckerController::class, 'createTask']);
    Route::post('updatetask', [UPSCheckerController::class, 'updateTask']);
    Route::get('gettask', [UPSCheckerController::class, 'getTask']);
    Route::post('addHeader', [UPSCheckerController::class, 'updateHeader']);
    Route::get('getheader', [UPSCheckerController::class, 'getHeader']);
    Route::get('getlistheader', [UPSCheckerController::class, 'getListHeader']);
    Route::post('getaccount', [UPSCheckerController::class, 'getAccount']);
    Route::post('downloadCsv', [UPSCheckerController::class, 'downloadCsv']);
    Route::get('getaccount', [UPSCheckerController::class, 'getAccountDetail']);
    Route::get('getCountry', [UPSCheckerController::class, 'getListCountry']);
    Route::get('filterzipcode', [UPSCheckerController::class, 'filterZipCode']);
    Route::post('deleteaccount', [UPSCheckerController::class, 'deleteAccount']);
});

Route::any('callback', [LabelController::class, 'callbackUrl']);
