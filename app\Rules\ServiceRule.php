<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\DB;

class ServiceRule implements ValidationRule
{
	private $userId = null;
	public function __construct(string $userId){
		$this->userId = $userId;
	}
	
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $sql = "select uid from
					user_service
					join services on services.id = user_service.service_id
					where user_id = :user_id
					and is_enable = 1
					and uid = :uid";
		$data = DB::selectOne($sql, [
			'user_id' => $this->userId,
			'uid' => $value
		]);
		if (!isset($data->uid)) {
			$fail("Class: " . $value . " not found");
		}
    }
}
