{"codeToName": {"32": "space", "160": "space", "33": "exclam", "34": "quotedbl", "35": "numbersign", "36": "dollar", "37": "percent", "38": "ampersand", "146": "quoteright", "40": "parenleft", "41": "parenright", "42": "asterisk", "43": "plus", "44": "comma", "45": "hyphen", "173": "hyphen", "46": "period", "47": "slash", "48": "zero", "49": "one", "50": "two", "51": "three", "52": "four", "53": "five", "54": "six", "55": "seven", "56": "eight", "57": "nine", "58": "colon", "59": "semicolon", "60": "less", "61": "equal", "62": "greater", "63": "question", "64": "at", "65": "A", "66": "B", "67": "C", "68": "D", "69": "E", "70": "F", "71": "G", "72": "H", "73": "I", "74": "J", "75": "K", "76": "L", "77": "M", "78": "N", "79": "O", "80": "P", "81": "Q", "82": "R", "83": "S", "84": "T", "85": "U", "86": "V", "87": "W", "88": "X", "89": "Y", "90": "Z", "91": "bracketleft", "92": "backslash", "93": "bracketright", "94": "asciicircum", "95": "underscore", "145": "quoteleft", "97": "a", "98": "b", "99": "c", "100": "d", "101": "e", "102": "f", "103": "g", "104": "h", "105": "i", "106": "j", "107": "k", "108": "l", "109": "m", "110": "n", "111": "o", "112": "p", "113": "q", "114": "r", "115": "s", "116": "t", "117": "u", "118": "v", "119": "w", "120": "x", "121": "y", "122": "z", "123": "braceleft", "124": "bar", "125": "braceright", "126": "asciitilde", "161": "exclamdown", "162": "cent", "163": "sterling", "165": "yen", "131": "florin", "167": "section", "164": "currency", "39": "<PERSON><PERSON><PERSON>", "147": "quotedblleft", "171": "guillemotleft", "139": "guil<PERSON>lle<PERSON>", "155": "guil<PERSON><PERSON><PERSON>", "150": "endash", "134": "dagger", "135": "daggerdbl", "183": "periodcentered", "182": "paragraph", "149": "bullet", "130": "quotesinglbase", "132": "quotedblbase", "148": "<PERSON><PERSON><PERSON><PERSON>", "187": "guil<PERSON><PERSON><PERSON>", "133": "ellipsis", "137": "perth<PERSON>and", "191": "questiondown", "96": "grave", "180": "acute", "136": "circumflex", "152": "tilde", "175": "macron", "168": "<PERSON><PERSON><PERSON>", "184": "cedilla", "151": "emdash", "198": "AE", "170": "ordfeminine", "216": "<PERSON><PERSON><PERSON>", "140": "OE", "186": "ordmasculine", "230": "ae", "248": "oslash", "156": "oe", "223": "germandbls", "207": "Idieresis", "233": "eacute", "159": "Ydieresis", "247": "divide", "221": "Ya<PERSON>", "194": "Acircumflex", "225": "aacute", "219": "Ucircumflex", "253": "yacute", "234": "ecircumflex", "220": "Udieresis", "218": "Uacute", "203": "Edieresis", "169": "copyright", "229": "aring", "224": "agrave", "227": "atilde", "154": "scaron", "237": "iacute", "251": "ucircumflex", "226": "acircumflex", "231": "ccedilla", "222": "Thorn", "179": "threesuperior", "210": "<PERSON><PERSON>", "192": "<PERSON><PERSON>", "215": "multiply", "250": "uacute", "255": "ydieresis", "238": "icircumflex", "202": "Ecircumflex", "228": "adieresis", "235": "edieresis", "205": "Iacute", "177": "plus<PERSON>us", "166": "brokenbar", "174": "registered", "200": "<PERSON><PERSON>", "142": "<PERSON><PERSON><PERSON>", "208": "Eth", "199": "Ccedilla", "193": "Aacute", "196": "Adieresis", "232": "egrave", "211": "Oacute", "243": "oacute", "239": "idieresis", "212": "Ocircumflex", "217": "<PERSON><PERSON>", "254": "thorn", "178": "twosuperior", "214": "Odieresis", "181": "mu", "236": "igrave", "190": "threequarters", "153": "trademark", "204": "<PERSON><PERSON>", "189": "onehalf", "244": "ocircumflex", "241": "ntilde", "201": "Eacute", "188": "onequarter", "138": "<PERSON><PERSON><PERSON>", "176": "degree", "242": "ograve", "249": "ugrave", "209": "Ntilde", "245": "otilde", "195": "<PERSON><PERSON>", "197": "<PERSON><PERSON>", "213": "<PERSON><PERSON><PERSON>", "206": "Icircumflex", "172": "logicalnot", "246": "odieresis", "252": "udieresis", "240": "eth", "158": "z<PERSON>on", "185": "onesuperior", "128": "Euro"}, "isUnicode": false, "FontName": "Helvetica-Bold", "FullName": "Helvetica Bold", "FamilyName": "Helvetica", "Weight": "Bold", "ItalicAngle": "0", "IsFixedPitch": "false", "CharacterSet": "ExtendedRoman", "FontBBox": ["-170", "-228", "1003", "962"], "UnderlinePosition": "-100", "UnderlineThickness": "50", "Version": "002.000", "EncodingScheme": "WinAnsiEncoding", "CapHeight": "718", "XHeight": "532", "Ascender": "718", "Descender": "-207", "StdHW": "118", "StdVW": "140", "StartCharMetrics": "317", "C": {"32": 278, "160": 278, "33": 333, "34": 474, "35": 556, "36": 556, "37": 889, "38": 722, "146": 278, "40": 333, "41": 333, "42": 389, "43": 584, "44": 278, "45": 333, "173": 333, "46": 278, "47": 278, "48": 556, "49": 556, "50": 556, "51": 556, "52": 556, "53": 556, "54": 556, "55": 556, "56": 556, "57": 556, "58": 333, "59": 333, "60": 584, "61": 584, "62": 584, "63": 611, "64": 975, "65": 722, "66": 722, "67": 722, "68": 722, "69": 667, "70": 611, "71": 778, "72": 722, "73": 278, "74": 556, "75": 722, "76": 611, "77": 833, "78": 722, "79": 778, "80": 667, "81": 778, "82": 722, "83": 667, "84": 611, "85": 722, "86": 667, "87": 944, "88": 667, "89": 667, "90": 611, "91": 333, "92": 278, "93": 333, "94": 584, "95": 556, "145": 278, "97": 556, "98": 611, "99": 556, "100": 611, "101": 556, "102": 333, "103": 611, "104": 611, "105": 278, "106": 278, "107": 556, "108": 278, "109": 889, "110": 611, "111": 611, "112": 611, "113": 611, "114": 389, "115": 556, "116": 333, "117": 611, "118": 556, "119": 778, "120": 556, "121": 556, "122": 500, "123": 389, "124": 280, "125": 389, "126": 584, "161": 333, "162": 556, "163": 556, "fraction": 167, "165": 556, "131": 556, "167": 556, "164": 556, "39": 238, "147": 500, "171": 556, "139": 333, "155": 333, "fi": 611, "fl": 611, "150": 556, "134": 556, "135": 556, "183": 278, "182": 556, "149": 350, "130": 278, "132": 500, "148": 500, "187": 556, "133": 1000, "137": 1000, "191": 611, "96": 333, "180": 333, "136": 333, "152": 333, "175": 333, "breve": 333, "dotaccent": 333, "168": 333, "ring": 333, "184": 333, "hungarumlaut": 333, "ogonek": 333, "caron": 333, "151": 1000, "198": 1000, "170": 370, "Lslash": 611, "216": 778, "140": 1000, "186": 365, "230": 889, "dotlessi": 278, "lslash": 278, "248": 611, "156": 944, "223": 611, "207": 278, "233": 556, "abreve": 556, "uhungarumlaut": 611, "ecaron": 556, "159": 667, "247": 584, "221": 667, "194": 722, "225": 556, "219": 722, "253": 556, "scommaaccent": 556, "234": 556, "Uring": 722, "220": 722, "aogonek": 556, "218": 722, "uogonek": 611, "203": 667, "Dcroat": 722, "commaaccent": 250, "169": 737, "Emacron": 667, "ccaron": 556, "229": 556, "Ncommaaccent": 722, "lacute": 278, "224": 556, "Tcommaaccent": 611, "Cacute": 722, "227": 556, "Edotaccent": 667, "154": 556, "scedilla": 556, "237": 278, "lozenge": 494, "Rcaron": 722, "Gcommaaccent": 778, "251": 611, "226": 556, "Amacron": 722, "rcaron": 389, "231": 556, "Zdotaccent": 611, "222": 667, "Omacron": 778, "Racute": 722, "Sacute": 667, "dcaron": 743, "Umacron": 722, "uring": 611, "179": 333, "210": 778, "192": 722, "Abreve": 722, "215": 584, "250": 611, "Tcaron": 611, "partialdiff": 494, "255": 556, "Nacute": 722, "238": 278, "202": 667, "228": 556, "235": 556, "cacute": 556, "nacute": 611, "umacron": 611, "Ncaron": 722, "205": 278, "177": 584, "166": 280, "174": 737, "Gbreve": 778, "Idotaccent": 278, "summation": 600, "200": 667, "racute": 389, "omacron": 611, "Zacute": 611, "142": 611, "greaterequal": 549, "208": 722, "199": 722, "lcommaaccent": 278, "tcaron": 389, "eogonek": 556, "Uogonek": 722, "193": 722, "196": 722, "232": 556, "zacute": 500, "iogonek": 278, "211": 778, "243": 611, "amacron": 556, "sacute": 556, "239": 278, "212": 778, "217": 722, "Delta": 612, "254": 611, "178": 333, "214": 778, "181": 611, "236": 278, "ohungarumlaut": 611, "Eogonek": 667, "dcroat": 611, "190": 834, "Scedilla": 667, "lcaron": 400, "Kcommaaccent": 722, "Lacute": 611, "153": 1000, "edotaccent": 556, "204": 278, "Imacron": 278, "Lcaron": 611, "189": 834, "lessequal": 549, "244": 611, "241": 611, "Uhungarumlaut": 722, "201": 667, "emacron": 556, "gbreve": 611, "188": 834, "138": 667, "Scommaaccent": 667, "Ohungarumlaut": 778, "176": 400, "242": 611, "Ccaron": 722, "249": 611, "radical": 549, "Dcaron": 722, "rcommaaccent": 389, "209": 722, "245": 611, "Rcommaaccent": 722, "Lcommaaccent": 611, "195": 722, "Aogonek": 722, "197": 722, "213": 778, "zdotaccent": 500, "Ecaron": 667, "Iogonek": 278, "kcommaaccent": 556, "minus": 584, "206": 278, "ncaron": 611, "tcommaaccent": 333, "172": 584, "246": 611, "252": 611, "notequal": 549, "gcommaaccent": 611, "240": 611, "158": 500, "ncommaaccent": 611, "185": 333, "imacron": 278, "128": 556}, "CIDtoGID_Compressed": true, "CIDtoGID": "eJwDAAAAAAE=", "_version_": 6}