<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\UspsService;

class UspsServiceTest extends TestCase
{
    protected UspsService $uspsService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->uspsService = new UspsService();
    }

    public function test_generate_tracking_number_format()
    {
        $reflection = new \ReflectionClass($this->uspsService);
        $method = $reflection->getMethod('generateTrackingNumber');
        $method->setAccessible(true);

        $trackingNumber = $method->invoke($this->uspsService);

        // Kiểm tra format: 9434 123456 1234 12
        $this->assertMatchesRegularExpression('/^\d{4} \d{6} \d{4} \d{2}$/', $trackingNumber);
        $this->assertStringStartsWith('9434', $trackingNumber);
    }

    public function test_tracking_number_is_unique()
    {
        $reflection = new \ReflectionClass($this->uspsService);
        $method = $reflection->getMethod('generateTrackingNumber');
        $method->setAccessible(true);

        $trackingNumber1 = $method->invoke($this->uspsService);
        $trackingNumber2 = $method->invoke($this->uspsService);

        // Kiểm tra hai tracking number khác nhau
        $this->assertNotEquals($trackingNumber1, $trackingNumber2);
    }
}
