<?php

namespace App\Http\Controllers;

use App\Http\Helper\PDFHelper;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\View;
use Imagick;
use ImagickException;
use Jurosh\PDFMerge\PDFMerger;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Smalot\PdfParser\Parser;

class ResizeLabelController extends Controller
{
    public function upload(Request $request): BinaryFileResponse|\Illuminate\Http\RedirectResponse
    {
        $request->validate([
            'pdf' => 'required|mimes:pdf|max:10000', // Validate the file
        ]);

        $data = $request->all();

        $file = $request->file('pdf');
        $filename = time() . '.' . $file->getClientOriginalExtension();
        $path = $file->storeAs('uploads', $filename);

        $pdfPath = storage_path('app/' . $path);

        if (!file_exists($pdfPath)) {
            return back()->with('error', 'File not found: ' . $pdfPath);
        }

        $outputPath = public_path('storage/uploads/crop/images');

        if (!file_exists($outputPath)) {
            mkdir($outputPath, 0777, true);
        }

        $outputPathPdf = public_path('storage/uploads/crop/pdf');

        if (!file_exists($outputPathPdf)) {
            mkdir($outputPathPdf, 0777, true);
        }

        // Set rotation angle
        $rotationAngle = 90; // For example, rotate by 90 degrees

        try {
            $imagickNumPage = new Imagick();
            $imagickNumPage->setResolution(300, 300); // Set the resolution (DPI)
            $imagickNumPage->readImage($pdfPath);
            $imagickNumPage->stripImage();

            $numPages = $imagickNumPage->getNumberImages();

            $pathPdfs = [];
            for ($page = 0; $page < $numPages; $page++) {
                Log::info('Processing started: ' . now());

                $imagick = new Imagick();
                $imagick->setResolution(300, 300); // Set the resolution (DPI)
                $imagick->readImage($pdfPath . "[$page]");
                $imagick->stripImage();
                Log::info('Processing ended: ' . now());

                // Set the background color
                $background = new Imagick();
                $background->newImage($imagick->getImageWidth(), $imagick->getImageHeight(), new \ImagickPixel('white'));
                $background->setImageFormat('jpeg');
                $background->setImageCompression(Imagick::COMPRESSION_JPEG);

                // Rotate the image
                $imagick->rotateImage(new \ImagickPixel('none'), $rotationAngle);

                $imagick->setImageAlphaChannel(\Imagick::ALPHACHANNEL_REMOVE); // Remove alpha channel
                $imagick->mergeImageLayers(\Imagick::LAYERMETHOD_FLATTEN); // Flatten the image

                // Composite the PDF image onto the backgroundimag
                $background->compositeImage($imagick, \Imagick::COMPOSITE_OVER, 0, 0);

                $imageName = time() . "-page-{$page}.png";
                $pathFileCrop = $outputPath . '/' . $imageName;
                $background->writeImage($pathFileCrop);

                $imagePath = $this->cropImage($pathFileCrop, $outputPath, $imageName, $data['carrier'] ?? 'fedex');

                // delete image crop
                unlink($pathFileCrop);

                // create pdf file
                $pathPdfs[] = $this->createPDF($imagePath, $outputPathPdf, $page);

                // delete image
                unlink(public_path($imagePath));

                $imagick->clear();
                $imagick->destroy();
                $background->clear();
                $background->destroy();

            }

            $imagickNumPage->clear();
            $imagickNumPage->destroy();

            unlink($pdfPath);

            // merge file

            $pdf = new PDFMerger;
            foreach ($pathPdfs as $pathPdf) {
                $pdf->addPDF($pathPdf, 'all');
                // unlink($pathPdf);
            }

            // Define a path to save the merged PDF
            $mergedPdfPath = storage_path('app/uploads/' . time() . '.pdf');

            // Merge the PDFs and save the result
            $pdf->merge('file', $mergedPdfPath);

            foreach ($pathPdfs as $pathPdf) {
                unlink($pathPdf);
            }

            // Return the merged PDF as a response
            return response()->download($mergedPdfPath)->deleteFileAfterSend(true);

        } catch (ImagickException $e) {
            Log::error($e->getMessage());
            return back()->with('error', 'Imagick error: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return back()->with('error', 'Error: ' . $e->getMessage());
        }
    }

    public function createPDF($pathImage, $outputPathPdf, $page): string
    {
        // Instantiate Dompdf with options
        $dompdf = PDFHelper::createTemplateFDF();

        // HTML content for the PDF
        $view = View::make('template_pdf.resize_pdf', ['url' => $pathImage])->render();

        // Load HTML content into Dompdf
        $dompdf->loadHtml($view);

        // Output PDF as a file (optional)
        // Render PDF (output)
        $dompdf->render();

        $fileName = time() . "-page-{$page}.pdf";
        $url_pdf = $outputPathPdf . '/' . $fileName;;

        // Save PDF to the server's filesystem
        file_put_contents($url_pdf, $dompdf->output());

        return $url_pdf;
    }

    /**
     * @throws ImagickException
     */
    public function cropImage($imagePath,$outputPath, $filename, $option = 'fedex'): string
    {
        // Set desired output dimensions in inches
        $widthInches = 4;
        $heightInches = 6;

        // Set resolution (DPI)
        $resolution = 320;
        if ($option == 'fedex') {
            $x = 110;
            $y = 110;
        } else {
            // $resolution = 320;
            $x = 425; //580
            $y = 165;
        }

        // Convert inches to pixels
        $widthPixels = $widthInches * $resolution;
        $heightPixels = $heightInches * $resolution;

        $imagick = new Imagick($imagePath);
        $imagick->setImageResolution($resolution, $resolution);
        $imagick->resampleImage($resolution, $resolution, \Imagick::FILTER_UNDEFINED, 1);


        // Crop the image
        $imagick->cropImage($widthPixels, $heightPixels, $x, $y);

        // Center the cropped area
        $imagick->setImagePage(0, 0, 0, 0); // Removes any offset information

        // Save the cropped image
        $outputPathImage = $outputPath . '/crop_' . $filename;
        $imagick->writeImage($outputPathImage);

        $imagick->clear();
        $imagick->destroy();

        return 'storage/uploads/crop/images/crop_' . $filename;
    }

    public function uploadTest(Request $request)
    {
        $request->validate([
            'pdf' => 'required|mimes:pdf|max:10000', // Validate the file
        ]);

        $file = $request->file('pdf');
        $filename = time() . '.' . $file->getClientOriginalExtension();
        $path = $file->storeAs('uploads', $filename);

        $pdfPath = storage_path('app/' . $path);

        if (!file_exists($pdfPath)) {
            return back()->with('error', 'File not found: ' . $pdfPath);
        }

        $outputPath = public_path('storage/uploads/crop/images');

        if (!file_exists($outputPath)) {
            mkdir($outputPath, 0777, true);
        }

        $outputPathPdf = public_path('storage/uploads/crop/pdf');

        if (!file_exists($outputPathPdf)) {
            mkdir($outputPathPdf, 0777, true);
        }
        Log::info('Processing started: ' . now());

        // Initialize the parser
        $parser = new Parser();

        // Load a PDF file
        $pdf = $parser->parseFile($pdfPath);

        // Extract text from each page
        $text = $pdf->getText();
        $isUps = str_contains($text, 'View/Print Label');

        // Set rotation angle
        $rotationAngle = 90;
        $imagickNumPage = new Imagick();
        $imagickNumPage->setResolution(300, 300);
        $imagickNumPage->readImage($pdfPath);

        // Loop through each page in the PDF
        foreach ($imagickNumPage as $i => $page) {
            // Rotate the current page by 90 degrees clockwise
            $page->rotateImage(new \ImagickPixel('none'), 90);

            // Optionally, strip metadata to optimize file size
            $page->stripImage();

            // Set image format (e.g., JPEG, PNG)
            $page->setImageFormat('jpeg');
            $page->setImageCompression(Imagick::COMPRESSION_JPEG);
            $page->setImageCompressionQuality(85);

            // Save the rotated page as an image
            $imageName = sprintf('rotated_output_page_%d.jpg', $i);
            $pathFileCrop = $outputPath . '/' . $imageName;
            $page->writeImage($pathFileCrop);

            $imagePath = $this->cropImage($pathFileCrop, $outputPath, $imageName, !$isUps ? 'fedex' : 'ups');

            // delete image crop
            unlink($pathFileCrop);

            // create pdf file
            $pathPdfs[] = $this->createPDF($imagePath, $outputPathPdf, $i);

            // delete image
            unlink(public_path($imagePath));
        }

        // Free resources
        $imagickNumPage->clear();
        $imagickNumPage->destroy();

        unlink($pdfPath);

        // merge file

        $pdf = new PDFMerger;
        foreach ($pathPdfs as $pathPdf) {
            $pdf->addPDF($pathPdf, 'all');
            // unlink($pathPdf);
        }

        // Define a path to save the merged PDF
        $mergedPdfPath = storage_path('app/uploads/' . time() . '.pdf');

        // Merge the PDFs and save the result
        $pdf->merge('file', $mergedPdfPath);


        Log::info('Processing ended: ' . now());

        // Return the merged PDF as a response
        return response()->download($mergedPdfPath)->deleteFileAfterSend(true);
    }
}
