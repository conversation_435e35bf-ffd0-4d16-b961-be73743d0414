<?php

namespace App\Http\Controllers;

use DateTime;
use GuzzleHttp\Client;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Process;
use UPS\OAuthClientCredentials\Configuration;
use UPS\OAuthClientCredentials\Request\DefaultApi;

class SettingController extends Controller
{
    public function updateSettings(Request $request): JsonResponse
    {
        $data = $request->all();
        DB::transaction(function () use ($data) {
            $clientId = "";
            $clientSecret = "";

            if (isset($data['setting'])) {
                DB::table('settings')->delete();
                foreach ($data['setting'] as $key => $item) {
                    DB::table('settings')->insert([
                        'key' => $item['key'],
                        'value' => $item['value'],
                        'order_no' => $key
                    ]);
                    if ($key == 2) {
                        $clientId = $item['value'];
                    }
                    if ($key == 3) {
                        $clientSecret = $item['value'];
                    }
                }

                $access_token = $this->getAccessToken($clientId, $clientSecret);
                DB::table('access_key')->where('key_', '=', 'crushippers')->update([
                    'value_' => $access_token
                ]);
            }
            if (isset($data['settingTeztship'])) {
                DB::table('settings_teztship')->delete();
                foreach ($data['settingTeztship'] as $key => $item) {
                    DB::table('settings_teztship')->insert([
                        'key' => $item['key'],
                        'value' => $item['value'],
                        'order_no' => $key
                    ]);
                    if ($key == 1) {
                        $clientId = $item['value'];
                    }
                    if ($key == 2) {
                        $clientSecret = $item['value'];
                    }
                }

                $access_token = $this->getAccessToken($clientId, $clientSecret);
                DB::table('access_key')->where('key_', '=', 'teztship')->update([
                    'value_' => $access_token
                ]);
            }
        });
        return response()->json([
            'status' => true
        ]);
    }

    public static function getAccessToken($clientId, $clientSecret) {
        $accessToken = "";
        try {
            $client = new Client([
                'headers' => [
                    'Authorization' => "Basic " . base64_encode("{$clientId}:{$clientSecret}")
                ]
            ]);

            $guzzleResponse = $client->post(
                'https://wwwcie.ups.com/security/v1/oauth/token', [
                'form_params' => [
                    'grant_type' => 'client_credentials',
                ]
            ]);
            if ($guzzleResponse->getStatusCode() == 200) {
                $response = json_decode($guzzleResponse->getBody(), true);
                return $response['access_token'];
            }
        } catch(\Exception $e){
            return $accessToken;
        }

    }
    public function getListSetting(Request $request): JsonResponse
    {
        $data = $request->all();
        $query = DB::table('settings');
        if (isset($data['key'])) {
            $query->where('key', '=', $data['key']);
        }

        $dataSetting = $query->orderBy('order_no')->get();

        $setting_teztship = DB::table('settings_teztship')->orderBy('order_no')->get();
        return response()->json([
            'settings' => $dataSetting,
            'setting_teztship' => $setting_teztship
        ]);
    }

    public function shutDownServiceApi(Request $request) {
        $data = $request->all();
        if ($data['server'] == 'curshippers') {
            $response = Http::post('http://************:8082/api/shutdown', $data);
        } elseif($data['server'] == 'teztship') {
            $response = Http::post('http://teztship.com:8001/api/shutdown', $data);
        }
        return true;
    }

    public function restartServiceApi(Request $request) {
        $data = $request->all();

        if ($data['server'] == 'curshippers') {
            $response = Http::post('http://************:8082/api/restart', $data);
        } elseif($data['server'] == 'teztship') {
            $response = Http::post('http://teztship.com:8001/api/restart', $data);
        }
        return true;
    }

    public function startServiceApi(Request $request) {
        $data = $request->all();

        if ($data['server'] == 'curshippers') {
            $response = Http::post('http://************:8082/api/start', $data);
        } elseif($data['server'] == 'teztship') {
            $response = Http::post('http://teztship.com:8001/api/start', $data);
        }
        return true;
    }

    public function shutDownService(Request $request): bool
    {
        $data = $request->all();
	    if ($data['type'] == 'iship') {
		    $batch_file = env('BATCH_ISHIP_SHUTDOWN');
		    $res = Process::run('c:\WINDOWS\system32\cmd.exe /c START ' . $batch_file);
            $res->output();
	    } elseif ($data['type'] == 'labelapi2') {
		    $batch_file = env('BATCH_LABEL_API2_SHUTDOWN');
		    Process::run('c:\WINDOWS\system32\cmd.exe /c START ' . $batch_file);
	    }
        return true;
    }

    public function restartService(Request $request): bool
    {
        $data = $request->all();
	    if ($data['type'] == 'iship') {
		    $batch_file = env('BATCH_ISHIP_SHUTDOWN');
		    Process::run('c:\WINDOWS\system32\cmd.exe /c START ' . $batch_file);

		    sleep(2);

		    $batch_file = env('BATCH_ISHIP_START');
		    Process::run('c:\WINDOWS\system32\cmd.exe /c START ' . $batch_file);
	    } elseif ($data['type'] == 'labelapi2') {
		    $batch_file = env('BATCH_LABEL_API2_SHUTDOWN');
		    Process::run('c:\WINDOWS\system32\cmd.exe /c START ' . $batch_file);

		    sleep(2);

		    $batch_file = env('BATCH_LABEL_API2_START');
		    Process::run('c:\WINDOWS\system32\cmd.exe /c START ' . $batch_file);

            $batch_file = env('BATCH_PYTHON_CUR_START');
		    Process::run('c:\WINDOWS\system32\cmd.exe /c START ' . $batch_file);

            $batch_file = env('BATCH_PYTHON_TEZTSHIP_START');
		    Process::run('c:\WINDOWS\system32\cmd.exe /c START ' . $batch_file);

            $batch_file = env('BATCH_PYTHON_SHIP2T_START');
		    Process::run('c:\WINDOWS\system32\cmd.exe /c START ' . $batch_file);
	    }
        return true;
    }

    public function startService(Request $request): bool
    {
        $data = $request->all();
	    if ($data['type'] == 'iship') {
		    $batch_file = env('BATCH_ISHIP_START');
		    Process::run('c:\WINDOWS\system32\cmd.exe /c START ' . $batch_file);
	    } elseif ($data['type'] == 'labelapi2') {
		    $batch_file = env('BATCH_LABEL_API2_START');
		    Process::run('c:\WINDOWS\system32\cmd.exe /c START ' . $batch_file);

            $batch_file = env('BATCH_PYTHON_CUR_START');
		    Process::run('c:\WINDOWS\system32\cmd.exe /c START ' . $batch_file);

            $batch_file = env('BATCH_PYTHON_TEZTSHIP_START');
		    Process::run('c:\WINDOWS\system32\cmd.exe /c START ' . $batch_file);

            $batch_file = env('BATCH_PYTHON_SHIP2T_START');
		    Process::run('c:\WINDOWS\system32\cmd.exe /c START ' . $batch_file);
	    }
        return true;
    }

    public function getListAccountNo(): JsonResponse
    {
        $account = DB::table('account_number')->orderBy('order_no')->get();
        $account_str = "";
		$lengthAccount = count($account);
        foreach ($account as $key => $item) {
            $account_str .= $item->account_no;
			if ($key != $lengthAccount - 1) {
				$account_str .= PHP_EOL;
			}
        }
		$settings_account_number = DB::table('settings_account_number')->where('key_', '=', 'limit_account')->first();
		$limitAccount = -1;
		if ($settings_account_number) {
			$limitAccount = $settings_account_number->value_;
		}
        return response()->json([
            'data' => $account_str,
	        'limitAccount' => $limitAccount
        ]);
    }

    public function updateListAccountNo(Request $request): bool
    {
		$data = $request->all();

	    $account_str = str_replace("\r\n", "\n", $data['account_str']);
	    $arr_account = explode("\n", $account_str);

	    DB::table('account_number')->truncate();

	    foreach ($arr_account as $key => $item) {
		    DB::table('account_number')->insert([
			    'account_no' => $item,
			    'date_create' => new DateTime(('now')),
			    'order_no' => $key
		    ]);
	    }
	    DB::table('settings_account_number')->where('key_', '=', 'limit_account')->update([
			'value_' => $data['limit_account']
	    ]);
	    if ($data['isRestartServer']) {
            DB::table('settings_account_number')->where('key_', '=', 'index_account')->update([
                'value_' => 0
            ]);

//		    Http::post('http://************:8082/api/restart', [
//			    'type' => 'labelapi2',
//			    'server' => 'curshippers'
//		    ]);
	    }
		return true;
    }

	public function getListAccountNoForShipease(): JsonResponse
	{
		$account = DB::connection('mysql_shipease')->table('account_number_shipease')->orderBy('order_no')->get();
		$account_str = "";
		$lengthAccount = count($account);
		foreach ($account as $key => $item) {
			$account_str .= $item->account_no;
			if ($key != $lengthAccount - 1) {
				$account_str .= PHP_EOL;
			}
		}

		$settings_account_number = DB::connection('mysql_shipease')->table('settings_shipease')->where('key_', '=', 'limit_time')->first();

		return response()->json([
			'data' => $account_str,
			'limit_time' => $settings_account_number->value_,
		]);
	}

	public function updateListAccountNoForShipease(Request $request): bool
	{
		$data = $request->all();

		$account_str = str_replace("\r\n", "\n", $data['account_str']);
		$arr_account = explode("\n", $account_str);

		DB::connection('mysql_shipease')->transaction(function () use ($arr_account) {
			DB::connection('mysql_shipease')->table('account_number_shipease')->delete();

			foreach ($arr_account as $key => $item) {
				DB::connection('mysql_shipease')->table('account_number_shipease')->insert([
					'account_no' => str_replace(' ', '', $item),
					'order_no' => $key
				]);
			}
		});

		DB::connection('mysql_shipease')->table('settings_shipease')->where('key_', '=', 'limit_time')->update([
			'value_' => $data['limit_time']
		]);
		if ($data['isRestartServer']) {
			DB::connection('mysql_shipease')->table('settings_shipease')->where('key_', '=', 'index_account')->update([
				'value_' => 0
			]);

			$response = Http::post('http://teztship.com:8001/api/restart', [
				'type' => 'labelapi2',
				'server' => 'teztship'
			]);
		}
		return true;
	}
}
