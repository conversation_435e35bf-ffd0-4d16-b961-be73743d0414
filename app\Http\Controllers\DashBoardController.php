<?php

namespace App\Http\Controllers;

use DateTime;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashBoardController extends Controller
{
    private const STATUS_TRANSACTION_ORDER = 2;
    private const STATUS_TRANSACTION_BALANCE_ADD = 1;
    private const STATUS_TRANSACTION_BALANCE_MINUS = 3;

    public function getDataDashboard(Request $request): JsonResponse
    {
        $query_params = $request->all();

        $query_order = DB::table('orders');

        $query_revenue = DB::table('transaction')->where('type', '=', self::STATUS_TRANSACTION_ORDER);
        $query_deposit = DB::table('transaction')->whereIn('type', [self::STATUS_TRANSACTION_BALANCE_ADD, self::STATUS_TRANSACTION_BALANCE_MINUS]);
        $query_user = DB::table('users')->where('is_admin', '<>', 1);;

        $query_last_order = DB::table('orders');
        $query_last_revenue = DB::table('transaction')->where('type', '=', self::STATUS_TRANSACTION_ORDER);
        $query_last_deposit = DB::table('transaction')->whereIn('type', [self::STATUS_TRANSACTION_BALANCE_ADD, self::STATUS_TRANSACTION_BALANCE_MINUS]);
        $query_last_user = DB::table('users')->where('is_admin', '<>', 1);

        $query_label_type = DB::table('orders')
            ->join('services', 'services.id', '=', 'orders.service_id');

        if (isset($query_params['dateStart']) && isset($query_params['dateEnd'])) {
            $query_order->whereBetween('orders.date_create', [$query_params['dateStart'], $query_params['dateEnd']]);
            $query_revenue->whereBetween('transaction.date_create', [$query_params['dateStart'], $query_params['dateEnd']]);
            $query_user->whereBetween('created_at', [$query_params['dateStart'], $query_params['dateEnd']]);
            $query_deposit->whereBetween('transaction.date_create', [$query_params['dateStart'], $query_params['dateEnd']]);

            $query_label_type->whereBetween('orders.date_create', [$query_params['dateStart'], $query_params['dateEnd']]);
        }

		if (isset($query_params['carrier_id'])) {
			$query_label_type->where('services.carrier_id', '=', $query_params['carrier_id']);
		}
        if (isset($query_params['dateLastStart']) && isset($query_params['dateLastEnd'])) {
            $query_last_order->whereBetween('orders.date_create', [$query_params['dateLastStart'], $query_params['dateLastEnd']]);
            $query_last_revenue->whereBetween('transaction.date_create', [$query_params['dateLastStart'], $query_params['dateLastEnd']]);
            $query_last_deposit->whereBetween('transaction.date_create', [$query_params['dateLastStart'], $query_params['dateLastEnd']]);
            $query_last_user->whereBetween('created_at', [$query_params['dateLastStart'], $query_params['dateLastEnd']]);
        }

        $count_order = $query_order->count('id');
        $count_revenue = -1 * $query_revenue->sum('amount');
        $count_user = $query_user->count('id');
        $count_deposit = 1 * $query_deposit->sum('amount');

        $query_label_type = $query_label_type
	        ->groupBy(['services.service_name'])
	        ->selectRaw('services.service_name, count(orders.id) as total_order');

        $orders_recent = DB::table('orders')
            ->leftJoin('services', 'services.id', '=', 'orders.service_id')
            ->leftJoin('users', 'users.id', '=', 'orders.user_id')
            ->orderByDesc('create_at')
            ->select(
                'orders.*',
                'services.service_name',
                'users.name as username'
            )->limit(10)->get();
		
		if ($query_params['viewYearAnalyticsSelect'] == 'Day') {
			$yearAnalytics =  $this->getYearOrderForDayAnalytics($query_params['dateStart'], $query_params['dateEnd']);
			$yearAmountAnalytics =  $this->getYearAmountForDayAnalytics($query_params['dateStart'], $query_params['dateEnd']);
		} else {
			$yearAnalytics =  $this->getYearOrderForWeekAnalytics($query_params['dateStart'], $query_params['dateEnd']);
			$yearAmountAnalytics =  $this->getYearAmountForWeekAnalytics($query_params['dateStart'], $query_params['dateEnd']);
		}
		
		if ($query_params['viewChartShipper'] == 'Day') {
			$orders_chart = $this->getShipperOrderForDay($query_params['dateStartShipper'], $query_params['dateEndShipper']);
	        $orders_refund = $this->getShipperOrderForDay($query_params['dateStartShipper'], $query_params['dateEndShipper'], 2);
	        $orders_delivery = $this->getShipperOrderForDay($query_params['dateStartShipper'], $query_params['dateEndShipper'], 1);
		} else {
			$orders_chart = $this->getShipperOrderForWeek($query_params['dateStartShipper'], $query_params['dateEndShipper']);
	        $orders_refund = $this->getShipperOrderForWeek($query_params['dateStartShipper'], $query_params['dateEndShipper'], 2);
	        $orders_delivery = $this->getShipperOrderForWeek($query_params['dateStartShipper'], $query_params['dateEndShipper'], 1);
		}
        return response()->json([
            'orders' => $count_order,
            'last_orders' => $query_last_order->count('id'),
            'revenue' => $count_revenue,
            'last_revenue' => -1 * $query_last_revenue->sum('amount'),
            'users' => $count_user,
            'last_user' => $query_last_user->count('id'),
            'deposit' => $count_deposit,
            'last_deposit' => 1 * $query_last_deposit->sum('amount'),
            'label_type' => $query_label_type->get(),
            'orders_recent' => $orders_recent,
            'orders_chart' => $orders_chart,
	        'orders_refund' => $orders_refund,
	        'orders_delivery' => $orders_delivery,
	        'yearAnalytics' => $yearAnalytics,
	        'yearAmountAnalytics' => $yearAmountAnalytics
        ]);
    }

	private function getShipperOrderForMonth($dateFrom, $dateTo, $status = null, $user_id = null): array
	{
		$param_where['dateFrom'] = $dateFrom;
		$param_where['dateTo'] = $dateTo;
		$where = "";
		if (isset($status)) {
			$param_where['status'] = $status;
			$where = " AND status = :status";
		}
		if (isset($user_id)) {
			$param_where['user_id'] = $user_id;
			$where = " AND user_id = :user_id";
		}
		$sql = "SELECT DATE_FORMAT(date_create, '%m/%Y') AS label, count(id) as total
            FROM orders
            WHERE date_create BETWEEN :dateFrom AND :dateTo ". $where ."
            GROUP BY DATE_FORMAT(date_create, '%m-%y')
						ORDER BY date_create";
		return DB::select($sql, $param_where);
	}
	
	private function getShipperOrderForDay($dateFrom, $dateTo, $status = null, $user_id = null): array
	{
		$param_where['dateFrom'] = $dateFrom;
		$param_where['dateTo'] = $dateTo;
		$where = "";
		if (isset($status)) {
			$param_where['status'] = $status;
			$where .= " AND status = :status";
		}
		if (isset($user_id)) {
			$param_where['user_id'] = $user_id;
			$where .= " AND user_id = :user_id";
		}
		$sql = "SELECT DATE_FORMAT(date_create, '%d/%m') AS label, count(id) as total
            FROM orders
            WHERE date_create BETWEEN :dateFrom AND :dateTo ". $where ."
            GROUP BY DATE_FORMAT(date_create, '%d-%m')
						ORDER BY date_create";
		return DB::select($sql, $param_where);
	}
	
	private function getShipperOrderForWeek($dateFrom, $dateTo, $status = null, $user_id = null): array
	{
		$param_where['dateFrom'] = $dateFrom;
		$param_where['dateTo'] = $dateTo;
		$where = "";
		if (isset($status)) {
			$param_where['status'] = $status;
			$where .= " AND status = :status";
		}
		if (isset($user_id)) {
			$param_where['user_id'] = $user_id;
			$where .= " AND user_id = :user_id";
		}
		$sql = "SELECT
					count(id) as total,
					CONCAT(DATE_FORMAT(DATE_ADD(firstOfMonth,INTERVAL (week-1) WEEK),'%d/%m') ,
					'~',
					DATE_FORMAT(IF(DATE_ADD(firstOfMonth,INTERVAL ((week-1)*7+6) DAY) > eom,
					      eom,
					      DATE_ADD(firstOfMonth,INTERVAL ((week-1)*7+6) DAY)),'%d/%m')
					) as label
					FROM (
					   SELECT
						 id,
					    date_create as date,
					    FLOOR((DAYOFMONTH(date_create) - 1) / 7 +1) AS week,
					    CONCAT('Week ',FLOOR((DAYOFMONTH(date_create) - 1) / 7) +1) AS weekname,
					    DATE_ADD(date_create,interval -DAY(date_create)+1 DAY) AS firstOfMonth,
					    LAST_DAY(date_create) as 'eom'
					FROM `orders`
					 WHERE date_create BETWEEN :dateFrom AND :dateTo
					". $where ."
					ORDER BY date_create
					) a
					GROUP BY a.week
					ORDER BY date";
		return DB::select($sql, $param_where);
	}
	
	private function getYearOrderForDayAnalytics($dateFrom, $dateTo, $user_id = null): array
	{
		$where_param['dateFrom'] = $dateFrom;
		$where_param['dateTo'] = $dateTo;
		$where = "";
		
		if (isset($user_id)) {
			$where_param['user_id'] = $user_id;
			$where = " AND user_id = :user_id";
		}
		$sql = "SELECT DATE_FORMAT(date_create, '%d/%m') AS label, ABS(SUM(amount)) as total_order
            FROM transaction
            WHERE date_create BETWEEN :dateFrom AND :dateTo
            AND type = 2
            ". $where . "
            GROUP BY DATE_FORMAT(date_create, '%d-%m')
            ORDER BY date_create";
		$data = DB::select($sql, $where_param);
		return $data;
	}
	
	private function getYearAmountForDayAnalytics($dateFrom, $dateTo, $user_id = null): array
	{
		$where_param['dateFrom'] = $dateFrom;
		$where_param['dateTo'] = $dateTo;
		$where = "";
		
		if (isset($user_id)) {
			$where_param['user_id'] = $user_id;
			$where = " AND user_id = :user_id";
		}
		$sql = "SELECT DATE_FORMAT(date_create, '%d/%m') AS label, ABS(SUM(amount)) as total_order
            FROM transaction
            WHERE date_create BETWEEN :dateFrom AND :dateTo
            ". $where. "
            AND (type = 1  or type = 3)
            GROUP BY DATE_FORMAT(date_create, '%d-%m')
            ORDER BY date_create";
		return DB::select($sql, $where_param);
	}
	
	private function getYearOrderForWeekAnalytics($dateFrom, $dateTo, $user_id = null): array
	{
		$where_param['dateFrom'] = $dateFrom;
		$where_param['dateTo'] = $dateTo;
		$where = "";
		
		if (isset($user_id)) {
			$where_param['user_id'] = $user_id;
			$where = " AND user_id = :user_id";
		}
		
		$sql = "SELECT
				abs(sum(amount)) as total_order,
				CONCAT(DATE_FORMAT(DATE_ADD(firstOfMonth,INTERVAL (week-1) WEEK),'%d/%m') ,
				'~',
				DATE_FORMAT(IF(DATE_ADD(firstOfMonth,INTERVAL ((week-1)*7+6) DAY) > eom,
				      eom,
				      DATE_ADD(firstOfMonth,INTERVAL ((week-1)*7+6) DAY)),'%d/%m')
				) as label
				FROM (
				   SELECT
					 amount,
				    date_create as date,
				    FLOOR((DAYOFMONTH(date_create) - 1) / 7 +1) AS week,
				    CONCAT('Week ',FLOOR((DAYOFMONTH(date_create) - 1) / 7) +1) AS weekname,
				    DATE_ADD(date_create,interval -DAY(date_create)+1 DAY) AS firstOfMonth,
				    LAST_DAY(date_create) as 'eom'
				FROM `transaction`
				 WHERE date_create BETWEEN :dateFrom AND :dateTo
				 ". $where ."
				 and type in (2)
				ORDER BY date_create
				) a
				GROUP BY a.week
				ORDER BY date";
		$data = DB::select($sql, $where_param);
		return $data;
	}
	
	private function getYearAmountForWeekAnalytics($dateFrom, $dateTo, $user_id = null): array
	{
		$where_param['dateFrom'] = $dateFrom;
		$where_param['dateTo'] = $dateTo;
		$where = "";
		
		if (isset($user_id)) {
			$where_param['user_id'] = $user_id;
			$where = " AND user_id = :user_id";
		}
		
		$sql = "SELECT
				abs(sum(amount)) as total_order,
				CONCAT(DATE_FORMAT(DATE_ADD(firstOfMonth,INTERVAL (week-1) WEEK),'%d/%m') ,
				'~',
				DATE_FORMAT(IF(DATE_ADD(firstOfMonth,INTERVAL ((week-1)*7+6) DAY) > eom,
				      eom,
				      DATE_ADD(firstOfMonth,INTERVAL ((week-1)*7+6) DAY)),'%d/%m')
				) as label
				FROM (
				   SELECT
					 amount,
				    date_create as date,
				    FLOOR((DAYOFMONTH(date_create) - 1) / 7 +1) AS week,
				    CONCAT('Week ',FLOOR((DAYOFMONTH(date_create) - 1) / 7) +1) AS weekname,
				    DATE_ADD(date_create,interval -DAY(date_create)+1 DAY) AS firstOfMonth,
				    LAST_DAY(date_create) as 'eom'
				FROM `transaction`
				 WHERE date_create BETWEEN :dateFrom AND :dateTo
				 and type in (1,3)
				 ". $where ."
				ORDER BY date_create
				) a
				GROUP BY a.week
				ORDER BY date";
		return DB::select($sql, $where_param);
	}

    public function getDashBoardUser(Request $request): JsonResponse
    {
	    $query_params = $request->all();
	    $user_id = $request->user()->id;
	    $query_order = DB::table('orders')->where('orders.user_id', '=', $user_id);
	    
	    $query_revenue = DB::table('transaction')
		    ->where('type', '=', self::STATUS_TRANSACTION_ORDER)
		    ->where('transaction.user_id', '=', $user_id);
	    
	    $query_deposit = DB::table('transaction')
		    ->whereIn('type', [self::STATUS_TRANSACTION_BALANCE_ADD, self::STATUS_TRANSACTION_BALANCE_MINUS])
		    ->where('transaction.user_id', '=', $user_id);
	    
	    $query_last_order = DB::table('orders');
	    $query_last_revenue = DB::table('transaction')
		    ->where('type', '=', self::STATUS_TRANSACTION_ORDER)
		    ->where('transaction.user_id', '=', $user_id);
	    
	    $query_last_deposit = DB::table('transaction')
		    ->whereIn('type', [self::STATUS_TRANSACTION_BALANCE_ADD, self::STATUS_TRANSACTION_BALANCE_MINUS])
		    ->where('transaction.user_id', '=', $user_id);
	    
	    $query_label_type = DB::table('orders')
		    ->join('services', 'services.id', '=', 'orders.service_id')
		    ->where('orders.user_id', '=', $user_id);
	    
	    if (isset($query_params['dateStart']) && isset($query_params['dateEnd'])) {
		    $query_order->whereBetween('orders.date_create', [$query_params['dateStart'], $query_params['dateEnd']]);
		    $query_revenue->whereBetween('transaction.date_create', [$query_params['dateStart'], $query_params['dateEnd']]);
		    $query_deposit->whereBetween('transaction.date_create', [$query_params['dateStart'], $query_params['dateEnd']]);
		    
		    $query_label_type->whereBetween('orders.date_create', [$query_params['dateStart'], $query_params['dateEnd']]);
	    }
	    
	    if (isset($query_params['carrier_id'])) {
		    $query_label_type->where('services.carrier_id', '=', $query_params['carrier_id']);
	    }
	    if (isset($query_params['dateLastStart']) && isset($query_params['dateLastEnd'])) {
		    $query_last_order->whereBetween('orders.date_create', [$query_params['dateLastStart'], $query_params['dateLastEnd']]);
		    $query_last_revenue->whereBetween('transaction.date_create', [$query_params['dateLastStart'], $query_params['dateLastEnd']]);
		    $query_last_deposit->whereBetween('transaction.date_create', [$query_params['dateLastStart'], $query_params['dateLastEnd']]);
	    }
	    
	    $count_order = $query_order->count('id');
	    $count_revenue = -1 * $query_revenue->sum('amount');
	    $count_deposit = 1 * $query_deposit->sum('amount');
	    
	    $query_label_type = $query_label_type
		    ->groupBy(['services.service_name'])
		    ->selectRaw('services.service_name, count(orders.id) as total_order');
	    
	    $orders_recent = DB::table('orders')
		    ->leftJoin('services', 'services.id', '=', 'orders.service_id')
		    ->leftJoin('users', 'users.id', '=', 'orders.user_id')
		    ->where('orders.user_id', '=', $user_id)
		    ->orderByDesc('create_at')
		    ->select(
			    'orders.*',
			    'services.service_name',
			    'users.name as username'
		    )->limit(10)->get();
	    
	    if ($query_params['viewYearAnalyticsSelect'] == 'Day') {
		    $yearAnalytics =  $this->getYearOrderForDayAnalytics($query_params['dateStart'], $query_params['dateEnd'], $user_id);
		    $yearAmountAnalytics =  $this->getYearAmountForDayAnalytics($query_params['dateStart'], $query_params['dateEnd'], $user_id);
	    } else {
		    $yearAnalytics =  $this->getYearOrderForWeekAnalytics($query_params['dateStart'], $query_params['dateEnd'], $user_id);
		    $yearAmountAnalytics =  $this->getYearAmountForWeekAnalytics($query_params['dateStart'], $query_params['dateEnd'], $user_id);
	    }
	    
	    if ($query_params['viewChartShipper'] == 'Day') {
		    $orders_chart = $this->getShipperOrderForDay($query_params['dateStartShipper'], $query_params['dateEndShipper'], null, $user_id);
		    $orders_refund = $this->getShipperOrderForDay($query_params['dateStartShipper'], $query_params['dateEndShipper'], 2, $user_id);
		    $orders_delivery = $this->getShipperOrderForDay($query_params['dateStartShipper'], $query_params['dateEndShipper'], 1, $user_id);
	    } else {
		    $orders_chart = $this->getShipperOrderForWeek($query_params['dateStartShipper'], $query_params['dateEndShipper'], null, $user_id);
		    $orders_refund = $this->getShipperOrderForWeek($query_params['dateStartShipper'], $query_params['dateEndShipper'], 2, $user_id);
		    $orders_delivery = $this->getShipperOrderForWeek($query_params['dateStartShipper'], $query_params['dateEndShipper'], 1, $user_id);
	    }
	    return response()->json([
		    'orders' => $count_order,
		    'last_orders' => $query_last_order->count('id'),
		    'revenue' => $count_revenue,
		    'last_revenue' => -1 * $query_last_revenue->sum('amount'),
		    'deposit' => $count_deposit,
		    'last_deposit' => 1 * $query_last_deposit->sum('amount'),
		    'label_type' => $query_label_type->get(),
		    'orders_recent' => $orders_recent,
		    'orders_chart' => $orders_chart,
		    'orders_refund' => $orders_refund,
		    'orders_delivery' => $orders_delivery,
		    'yearAnalytics' => $yearAnalytics,
		    'yearAmountAnalytics' => $yearAmountAnalytics
	    ]);
    }

    private function getDataCountOrderUser($year, $user_id): array
    {
        $sql = "SELECT
                    IFNULL(SUM(IF (month = 'Jan', total, 0)), 0) AS 'Jan'
                    , IFNULL(SUM(IF (month = 'Feb', total, 0)), 0) AS 'Feb'
                    , IFNULL(SUM(IF (month = 'Mar', total, 0)), 0) AS 'Mar'
                    , IFNULL(SUM(IF (month = 'Apr', total, 0)), 0) AS 'Apr'
                    , IFNULL(SUM(IF (month = 'May', total, 0)), 0) AS 'May'
                    , IFNULL(SUM(IF (month = 'Jun', total, 0)), 0) AS 'Jun'
                    , IFNULL(SUM(IF (month = 'Jul', total, 0)), 0) AS 'Jul'
                    , IFNULL(SUM(IF (month = 'Aug', total, 0)), 0) AS 'Aug'
                    , IFNULL(SUM(IF (month = 'Sep', total, 0)), 0) AS 'Sep'
                    , IFNULL(SUM(IF (month = 'Oct', total, 0)), 0) AS 'Oct'
                    , IFNULL(SUM(IF (month = 'Nov', total, 0)), 0) AS 'Nov'
                    , IFNULL(SUM(IF (month = 'Dec', total, 0)), 0) AS 'Dec'
                FROM
                    (
                        SELECT
                            DATE_FORMAT(date_create, '%b') AS month
                            , ABS(COUNT(id)) as total
                        FROM
                            orders
                        WHERE
                            YEAR (date_create) = ". $year ."
                            AND user_id = ". $user_id ."
                        GROUP BY
                            DATE_FORMAT(date_create, '%m-%Y')
                    ) as sub
                UNION all
                SELECT
                    IFNULL(SUM(IF (month = 'Jan', total, 0)), 0) AS 'Jan'
                    , IFNULL(SUM(IF (month = 'Feb', total, 0)), 0) AS 'Feb'
                    , IFNULL(SUM(IF (month = 'Mar', total, 0)), 0) AS 'Mar'
                    , IFNULL(SUM(IF (month = 'Apr', total, 0)), 0) AS 'Apr'
                    , IFNULL(SUM(IF (month = 'May', total, 0)), 0) AS 'May'
                    , IFNULL(SUM(IF (month = 'Jun', total, 0)), 0) AS 'Jun'
                    , IFNULL(SUM(IF (month = 'Jul', total, 0)), 0) AS 'Jul'
                    , IFNULL(SUM(IF (month = 'Aug', total, 0)), 0) AS 'Aug'
                    , IFNULL(SUM(IF (month = 'Sep', total, 0)), 0) AS 'Sep'
                    , IFNULL(SUM(IF (month = 'Oct', total, 0)), 0) AS 'Oct'
                    , IFNULL(SUM(IF (month = 'Nov', total, 0)), 0) AS 'Nov'
                    , IFNULL(SUM(IF (month = 'Dec', total, 0)), 0) AS 'Dec'
                FROM
                    (
                        SELECT
                            DATE_FORMAT(date_create, '%b') AS month
                            , ABS(COUNT(id)) as total
                        FROM
                            orders
                        WHERE
                            YEAR (date_create) = ". $year ."
                            AND user_id = ". $user_id ."
                            AND `status` = 1
                        GROUP BY
                            DATE_FORMAT(date_create, '%m-%Y')
                    ) as sub
                UNION all
                SELECT
                    IFNULL(SUM(IF (month = 'Jan', total, 0)), 0) AS 'Jan'
                    , IFNULL(SUM(IF (month = 'Feb', total, 0)), 0) AS 'Feb'
                    , IFNULL(SUM(IF (month = 'Mar', total, 0)), 0) AS 'Mar'
                    , IFNULL(SUM(IF (month = 'Apr', total, 0)), 0) AS 'Apr'
                    , IFNULL(SUM(IF (month = 'May', total, 0)), 0) AS 'May'
                    , IFNULL(SUM(IF (month = 'Jun', total, 0)), 0) AS 'Jun'
                    , IFNULL(SUM(IF (month = 'Jul', total, 0)), 0) AS 'Jul'
                    , IFNULL(SUM(IF (month = 'Aug', total, 0)), 0) AS 'Aug'
                    , IFNULL(SUM(IF (month = 'Sep', total, 0)), 0) AS 'Sep'
                    , IFNULL(SUM(IF (month = 'Oct', total, 0)), 0) AS 'Oct'
                    , IFNULL(SUM(IF (month = 'Nov', total, 0)), 0) AS 'Nov'
                    , IFNULL(SUM(IF (month = 'Dec', total, 0)), 0) AS 'Dec'
                FROM
                    (
                        SELECT
                            DATE_FORMAT(date_create, '%b') AS month
                            , ABS(COUNT(id)) as total
                        FROM
                            orders
                        WHERE
                            YEAR (date_create) = ". $year ."
                            AND user_id = ". $user_id ."
                            AND `status` = 2
                        GROUP BY
                            DATE_FORMAT(date_create, '%m-%Y')
                    ) as sub
                ";
        return DB::select($sql);
    }
}
