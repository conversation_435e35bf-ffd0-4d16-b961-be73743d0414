<?php

namespace App\Http\Controllers;

use DateTime;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class PLDController extends Controller
{
    public function createFilePLD(Request $request): JsonResponse
    {
        $data = $request->all();
        $lstOrder = $data['orders'];

        $dataOrder = DB::table('orders')->whereIn('id', $lstOrder)->get()->toArray();
        $url_api_create_pld = env('URL_CREATE_PLD_FILE');

        $sql = "select book_num, IF(used = 0,1,used) as used from assigned_book_numbers
                where (used + :max1) <= 99 ORDER BY RAND() limit 1";
        $books = DB::selectOne($sql, ['max1' => count($dataOrder)]);
        if (!$books) {
            return response()->json([
                'status' => true,
            ]);
        }
        $response = Http::post($url_api_create_pld, [
            'dataOrder' => $dataOrder,
            'book_number' => $books->book_num,
            'used' => $books->used,
        ]);
        $body = json_decode($response->body());
        if (isset($body->used)) {
            DB::table('assigned_book_numbers')->where('book_num', '=', $books->book_num)
                ->update(['used' => $body->used - 1]);
        }
        if (isset($body->dataRs)) {
            foreach ($body->dataRs as $rs) {
                $trackingNumber = $rs->tracking_number;
                $dataTracking = DB::table('work_pld_file_process')
                    ->where('tracking', '=', $trackingNumber)
                    ->first();

                $data = [
                    'path_file' => $rs->path_file,
                    'status' => 'New',
                    'create_at' => now(), // Using Laravel's helper for current time
                    'attempts' => 0,
                ];

                if ($dataTracking) {
                    if ($dataTracking->status !== 'Success') {
                        // Update existing record if status is not 'Success'
                        DB::table('work_pld_file_process')->updateOrInsert(
                            ['tracking' => $trackingNumber],
                            $data
                        );
                    }
                } else {
                    // Insert new record if not found
                    DB::table('work_pld_file_process')->insert(
                        array_merge(['tracking' => $trackingNumber], $data) // Merge data with tracking
                    );
                }
            }
        }
        return response()->json([
            'status' => true,
        ]);
    }

    public function getPldList(Request $request): JsonResponse
    {
        $query_params = $request->all();
        $query = DB::table('work_pld_file_process');

        if (isset($query_params['status']) && $query_params['status'] != 'All') {
            $query->where('work_pld_file_process.status', '=', $query_params['status']);
        }

        if (isset($query_params['tracking'])) {
            $query->where('work_pld_file_process.tracking', '=', $query_params['tracking']);
        }

        if (isset($query_params['sortby']) && $query_params['sortby'] == 2) {
            $query->orderByDesc('work_pld_file_process.create_at');
        } else {
            $query->orderBy('work_pld_file_process.create_at');
        }

        $orders_refund = $query->select(
            'work_pld_file_process.*'
        )->paginate($query_params['itemsPerPage'] ?? 10);
        return response()->json([
            'status' => true,
            'pld_list' => $orders_refund
        ]);
    }

    public function deletePld(Request $request): JsonResponse
    {
        $dataParams = $request->all();
        DB::table('work_pld_file_process')
            ->where('work_pld_file_process.tracking', '=', $dataParams['tracking'])
            ->delete();

        return response()->json([
            'status' => true,
        ]);
    }

    public function findAccount(Request $request): JsonResponse
    {
        $request->validate([
            'account_list' => 'required|string',
        ]);

        $account_list = explode(PHP_EOL, $request->get('account_list'));

        $url_api = 'http://193.26.115.177:3900/find_account';
        $response = Http::post($url_api, [
            'account_list' => $account_list,
        ]);
        $body = json_decode($response->body());
        if (isset($body)) {
            $result = implode(PHP_EOL, $body->result);
            return response()->json(['result' => $result]);
        }
        return response()->json(['message' => 'Account not found', 'status' => 404], 500);
    }
}
