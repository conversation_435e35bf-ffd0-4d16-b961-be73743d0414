<?php

namespace App\Http\Controllers;

use App\Rules\PriceRule;
use App\Rules\ServiceRule;
use DateTime;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Intervention\Image\ImageManagerStatic as Image;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

const REGEX_INVALID_CHAR_CSV = '/(^\s*")|("\s*$)|(^\s*)|((?<=^)[\n\r](?=$))|(\s*$)/';
class BulkOrderController extends Controller
{
	private const STATUS_ORDER_CANCEL = 3;
	private const STATUS_ORDER_DONE = 1;
	private const STATUS_TRANSACTION_ORDER = 2;

	public function validateCsv(Request $request): JsonResponse
	{
		$data = $request->all();
		$userId = $request->user()->id;

		$token_id = uniqid();
		$file_name = '/bulk_order/' . $userId . '/' . $token_id . '/' . $data['file_name'];
		Storage::disk('local')->put($file_name, base64_decode($data['file_csv']));
		$fileCSV = storage_path('app/' . $file_name);

		$validator = Validator::make($data, [
			'file_csv' => 'required',
			'file_name' => 'required',
		]);

		$validator->after(function ($validator) use ($fileCSV) {
			if (!$this->checkExcelFile(pathinfo($fileCSV, PATHINFO_EXTENSION))) {
				$validator->errors()->add('file', 'The file must be a file of type: csv, xlsx, xls');
			}
		});

		if ($validator->fails()) {
			Storage::disk('local')->delete($file_name);
			return response()->json([
				'status' => 422,
				'message' => [
					[
						'index' => 0,
						'msg' => $validator->errors()->messages()
					]
				]
			]);
		}

		$rowCsv = BulkOrderController::_csv_row_count($fileCSV);
		if ($rowCsv <= 0) {
			return response()->json([
				'status' => 422,
				'message' => [
					[
						'index' => 0,
						'msg' => ['No data create bulk']
					]
				]
			]);
		}

		$mapColCsv = [
			'Class',
			'FromName',
			'FromCompany',
			'FromStreet',
			'FromStreet2',
			'FromZip',
			'FromCity',
			'FromState',
			'FromPhone',
			'ToName',
			'ToCompany',
			'ToStreet',
			'ToStreet2',
			'ToZip',
			'ToCity',
			'ToState',
			'ToPhone',
			'Weight',
			'Length',
			'Width',
			'Height',
			'Description',
		];

		// slice data
		if (($handle = fopen($fileCSV, "r")) === FALSE) {
			return response()->json([
				'status' => 422,
				'message' => [
					[
						'index' => 0,
						'msg' => ['Error read file']
					]
				]
			]);
		}
		$row = 0;
		$errors = [];
		$dataCreateCsv = [];
		$errorRow = false;
		$totalPrice = 0;
		while (($current_row_data = fgets($handle)) !== FALSE) {
			// Grab headings.
			if ($row == 0) {
				$row++;
				continue;
			}
			try {
				$current_row_data = trim(preg_replace(REGEX_INVALID_CHAR_CSV, "", $current_row_data));
				$dataRow = explode(",", $current_row_data);

				if (count($mapColCsv) == count($dataRow)) {
					// Convert row data string to array
					$rowDataArr = array_map(function ($value) {
						return $value;
					}, array_combine($mapColCsv, $dataRow));
				} else {
					return response()->json([
						'status' => 422,
						'message' => [
							[
								'index' => 0,
								'msg' => ['File is valid']
							]
						]
					]);
				}
			} catch (Exception $ex) {
				return response()->json([
					'status' => 422,
					'message' => [
						[
							'index' => 0,
							'msg' => ['File is valid']
						]
					]
				]);
			}

			$validator = Validator::make($rowDataArr, [
				'Class' => ['required', new ServiceRule($userId)],
				'FromName' => 'required',
				'FromStreet' => 'required',
				'FromZip' => 'required',
				'FromCity' => 'required',
				'FromState' => 'required|max:2',
				'ToName' => 'required',
				'ToStreet' => 'required',
				'ToZip' => 'required',
				'ToCity' => 'required',
				'ToState' => 'required|max:2',
				'Weight' => ['required', 'numeric'],
				'Length' => 'required|numeric',
				'Width' => 'required|numeric',
				'Height' => 'required|numeric',
			]);

			$validator->after(function ($validator) use (&$rowDataArr, $userId) {
				$dataPrice = $this->getPrice($rowDataArr, $userId);
				if ($dataPrice === null) {
					$validator->errors()->add('Price', 'Price for weight ' . $rowDataArr['Weight'] . ' not found');
				} else {
					$rowDataArr['Price'] = $dataPrice->price;
				}
			});

			if ($validator->fails()) {
				$errors[] = [
					'index' => $row,
					'msg' => $validator->errors()->messages()
				];
				$errorRow = true;
			} else {
				$errors[] = [
					'index' => $row,
					'msg' => []
				];
			}
			$totalPrice += $rowDataArr['Price'] ?? 0;
			$dataCreateCsv[] = array_merge($rowDataArr, [
				'user_id' => $userId,
				'token_id' => $token_id,
			]);
			$row++;
		}
		if ($errorRow) {
			Storage::disk('local')->delete($file_name);
			return response()->json([
				'status' => 422,
				'message' => $errors
			]);
		}

		if ($totalPrice > $request->user()->balance) {
			return response()->json([
				'status' => 422,
				'message' => [
					[
						'index' => 0,
						'msg' => ['Not enough money']
					]
				]
			]);
		}
		DB::table('bulk_order')->insert($dataCreateCsv);
		Storage::disk('local')->delete($file_name);
		return response()->json([
			'status' => 422,
			'message' => $errors,
			'totalPrice' => $totalPrice,
			'token_id' => $token_id
		]);
	}

	public function createBulkOrder(Request $request)
	{
		$data = $request->all();
		$user_id = $request->user()->id;
		$validator = Validator::make($data, [
			'token_id' => 'required',
		]);

		if ($validator->fails()) {
			return response()->json([
				'status' => 422,
				'message' => $validator->errors()->messages()
			]);
		}

		$dataCsv = DB::table('bulk_order')
			->where('user_id', '=', $user_id)
			->where('token_id', '=', $data['token_id'])
			->get();
		if (count($dataCsv) <= 0) {
			return response()->json([
				'status' => 422,
				'message' => 'Data not found'
			]);
		}
		$totalPrice = 0;
		foreach ($dataCsv as $item) {
			$totalPrice += $item->Price;
		}
		if ($totalPrice > $request->user()->balance) {
			return response()->json([
				'status' => 422,
				'message' => 'Not enough money'
			]);
		}

		$labelImage = [];
		$current_balance = $request->user()->balance;
		$lstOrderID = [];
		foreach ($dataCsv as $item) {
			$new_date = new DateTime(('now'));
			// create order
			$order_id = uniqid() . '_' . date_format($new_date, 'YMdhms');
			$lstOrderID[] = $order_id;

			$service = DB::table('services')->where('uid', '=', $item->Class)
				->first();
			if ($service === null) {
				return response()->json([
					'status' => 422,
					'message' => 'Service not found'
				]);
			}

			DB::table('orders')->insert([
				'id' => $order_id,
				'date_create' => $new_date,
				'from_name' => $item->FromName,
				'to_name' => $item->ToName,
				'no_of_package' => 1,
				'weight' => $item->Weight,
				'length' => $item->Length,
				'width' => $item->Width,
				'height' => $item->Height,
				'ref1' => $item->Description ?? '',
				'ref2' => '',
				'saturday_delivery' => 0,
				'signature' => 0,
				'service_id' => $service->id,
				'to_company' => $item->ToCompany ?? '',
				'to_phone' => $item->ToPhone ?? '',
				'to_country' => 'US',
				'to_address1' => $item->ToStreet,
				'to_address2' => $item->ToStreet2,
				'to_zip' => $item->ToZip,
				'to_city' => $item->ToCity,
				'to_state' => $item->ToState,
				'from_company' => $item->FromCompany,
				'from_country' => 'US',
				'from_phone' => $item->FromPhone,
				'from_address1' => $item->FromStreet,
				'from_address2' => $item->FromStreet2 ?? '',
				'from_zip' => $item->FromZip,
				'from_city' => $item->FromCity,
				'from_state' => $item->FromState,
				'status' => self::STATUS_ORDER_CANCEL,
				'price' => $item->Price,
				'user_id' => $user_id,
				'tracking_status' => 'Pre_transit',
				'create_at' => $new_date,
				'delivery_date' => $new_date,
				'discount' => 0,
				'total_price' => $item->Price,
				'create_type' => 'csv'
			]);

            $dataBookLabel = [
                "service_name" => $service->service_name,
                "package" => [
                    "signature" => 0,
                    'ref1' => $item->Description ?? '',
                    'ref2' => '',
                    'weight' => $item->Weight,
                    'length' => $item->Length,
                    'width' => $item->Width,
                    'height' => $item->Height,
                    'saturdayDelivery' => 0,
                ],
                'dataFrom' => [
                    'address1' => $item->FromStreet,
                    'address2' => $item->FromStreet2 ?? '',
                    'city' => $item->FromCity,
                    'state' => $item->FromState,
                    'name' => $item->FromName,
                    'email' => '',
                    'zip' => $item->FromZip,
                    'companyName' => $item->FromCompany ?? '',
                    'phone' => $item->FromPhone ?? '',
                    'fax' => '',
                    'country' => 'United States',
                    'countryName' => 'US',
                ],
                'dataTo' => [
                    'companyName' => $item->ToCompany ?? '',
                    'name' => $item->ToName,
                    'address1' => $item->ToStreet,
                    'address2' => $item->ToStreet2 ?? '',
                    'city' => $item->ToCity,
                    'state' => $item->ToState,
                    'email' => '',
                    'fax' => '',
                    'phone' => $item->ToPhone ?? '',
                    'zip' => $item->ToZip,
                    'countryCode' => 'United States',
                    'res' => false
                ],
                'user_id' => $user_id,
            ];

            if ($service->provider == 'pirreas.com') {
                $rs = LabelController::getLabelPirreas($dataBookLabel, $service);
            } else if ($service->provider == 'api_python') {
                $rs = LabelController::getLabelPython($dataBookLabel, $service);
            } else {
                $rs = LabelController::getLabelApi2($dataBookLabel);
            }

            if($rs['tracking_no'] == '') {
                return response()->json([
                    'status' => 422,
                    'message' => 'Cannot create label. Please try again'
                ]);
            }
			$labelImage[] = $rs['file_name'];

			// update current balance
			$after_balance =  $current_balance - $item->Price;
			DB::table('users')
				->where('id', '=', $user_id)
				->update(['balance' => $after_balance]);

			$current_balance = $after_balance;

			DB::table('transaction')->insert([
				'id' => uniqid('TRANS_'). date_format($new_date, 'YMdhms'),
				'type' => self::STATUS_TRANSACTION_ORDER,
				'user_id' => $user_id,
				'amount' => -$item->Price,
				'current_amount' => $after_balance,
				'note' => 'Order created successfully.'. PHP_EOL .' Order ID: ' . $order_id ,
				'create_at' => $new_date,
				'date_create' => $new_date
			]);

			// update order
			DB::table('orders')->where('id', '=', $order_id)
				->update([
					'status' => self::STATUS_ORDER_DONE,
					'tracking_id' => $rs['tracking_no'],
					'label_image' => $rs['file_name']
			]);
			DB::table('bulk_order')->where('id', '=', $item->id)
				->delete();
		}
		return response()->json([
			'status' => 200,
			'current_balance' => $current_balance,
			'data' => $labelImage,
			'order' => DB::table('orders')
				->join('services', 'services.id', '=', 'orders.service_id')
				->whereIn('orders.id', $lstOrderID)
				->select('orders.*', 'services.service_name')->get()
		]);
	}

	private function convertImage($file_name): void
	{

		$url = Storage::disk('public')->path($file_name);

		// Load the image using Intervention/Image package
		$image = Image::make($url);

		// Rotate the image (you can specify the rotation angle)
		$rotatedImage = $image->rotate(90);
		$rotatedImage->save($url);
	}
	public function getLabelApi2($item, $service, $user_id)
	{
		$url_get_label = env('URL_API_LABEL2');
		$response = Http::post($url_get_label . "/create", [
			'pkgInfo' => [
				'serviceName' => $service->service_name,
				'countryCode' => 'US',
				'Signature' => 0,
				'carbon' => false,
				'demoFlag' => false,
				'reference1' => $item->Description ?? '',
				'reference2' => '',
				'weight' => $item->Weight,
				'length' => $item->Length,
				'width' => $item->Width,
				'height' => $item->Height,
				'barcoderef' => false
			],
			'fromCustomer' => [
				'address1' => $item->FromStreet,
				'address2' => $item->FromStreet2 ?? '',
				'address3' => '',
				'city' => $item->FromZip,
				'state' => $item->FromCity,
				'name' => $item->FromName,
				'email' => '',
				'postalCode' => $item->FromZip,
				'companyName' => $item->FromCompany ?? '',
				'phone' => $item->FromPhone ?? '',
				'fax' => '',
				'country' => 'United States',
				'countryName' => 'US',
			],
			'toCustomer' => [
				'companyName' => $item->ToCompany ?? '',
				'name' => $item->ToName,
				'address1' => $item->ToStreet,
				'address2' => $item->ToStreet2 ?? '',
				'address3' => '',
				'city' => $item->ToCity,
				'state' => $item->ToState,
				'email' => '',
				'fax' => '',
				'phone' => $item->ToPhone ?? '',
				'postalCode' => $item->ToZip,
				'countryCode' => 'United States',
				'res' => false
			],
			'accountNumber' => 'real',
			'satDelivery' => false,
		]);
        $dataBody = json_decode($response->body());
        $labelBase64 = $dataBody->label;
        $file_name = '/orders/' . $user_id . '/' . uniqid() . '.png';
        Storage::disk('public')->put($file_name, base64_decode($labelBase64));

        $this->convertImage($file_name);

        // convert image to pdf
        $format_date = date_format(new DateTime(('now')), 'Ymdhms');
        $pdf_file_name = '/orders/' . $user_id . '/' . uniqid() . '_' . $format_date . '.pdf';

        $data['routeCode'] = $dataBody->routeCode;
        $data['lblservicename'] = $dataBody->lblservicename;
        $data['label_image'] = $file_name;
        $data['tracking_no'] = $dataBody->tracking;

        PdfController::genPDFCrushippers($data, Storage::disk('public')->path($pdf_file_name));

        Storage::disk('public')->delete($file_name);
        return [
            'tracking_no' => $dataBody->tracking,
            'file_name' => $pdf_file_name
        ];
	}

	private function getPrice($data, $userId)
	{
		$sql = "SELECT DISTINCT
                custom_price.price
                FROM services
                INNER JOIN user_service ON services.id = user_service.service_id
                INNER JOIN custom_price ON services.id = custom_price.service_id
                WHERE services.`status` = 1
                AND services.is_deleted <> 1
                AND services.uid = :uid
                AND  user_service.user_id = :user_id
                AND :weightInputFrom >= custom_price.weight_from AND :weightInputTo <= custom_price.weight_to ";
		return DB::selectOne($sql, [
			'uid' => $data['Class'],
			'user_id' => $userId,
			'weightInputFrom' => $data['Weight'],
			'weightInputTo' => $data['Weight']
		]);
	}

	/**
	 * Count the number of rows in a CSV file excluding header row.
	 *
	 * @param string $filename
	 *   CSV filename.
	 *
	 * @return int
	 *   Number of rows.
	 */
	public static function _csv_row_count(string $filename): int
	{
		ini_set('auto_detect_line_endings', TRUE);
		$row_count = 0;
		if (($handle = fopen($filename, "r")) !== FALSE) {
			while (($row_data = fgets($handle, 2000)) !== FALSE) {
				$row_count++;
			}
			fclose($handle);
			// Exclude the headings.
			$row_count--;
			return $row_count;
		}
	}

	function checkExcelFile($file_ext): bool
	{
		$valid = array(
			'csv', 'xls', 'xlsx' // add your extensions here.
		);
		return in_array($file_ext, $valid);
	}

	public function downloadTemplate(): BinaryFileResponse
	{
		$path = storage_path('app/public/template/template.csv');

		return response()->download($path, 'example.csv', [
			'Content-Type' => 'text/csv',
			'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
			'Expires' => '0',
			'Pragma' => 'public',
		]);
	}
}
