<?php

namespace App\Jobs;

use DateTime;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CheckAccount implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private string $process_id;
    /**
     * Create a new job instance.
     */
    public function __construct($_process_id)
    {
        $this->process_id = $_process_id;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $process_id = $this->process_id;
        // Call the command directly
        $this->updateProcessStatus($process_id, 'Processing');

        $dataProcess = $this->getProcessData($process_id);
        if (empty($dataProcess)) {
            $this->logError($process_id, 'Data Process is empty');
            return;
        }

        $lines = $this->readFileData('D:\WEB\project\starter-kit-api\public\storage//ups_checker/66e5d4fca5721/todo_accounts_bk - Copy.txt'); // C:\file\list.txt
        $index = $dataProcess->current_index ?? 0;

        while ($index < count($lines)) {
            $account = $lines[$index];
            try {
                if ($this->isProcessStopped($process_id)) {
                    $this->updateProcessStatus($process_id, 'Success', 'stop');
                    $this->logInfo($process_id, 'Processing stop');
                    break;
                }

                if ($this->accountExists($account)) {
                    $index++;
                    $this->updateCurrentIndex($process_id, $index);
                    $this->logError($process_id, 'Account ' . $account . ' exists');
                    continue;
                }

                if ($this->processAccount($process_id, $account, $dataProcess)) {
                    $index++;
                    $this->updateCurrentIndex($process_id, $index);
                }
            } catch (\Exception $ex) {
                $this->logError($process_id, 'Process Account: ' . $account . ' Error: ' . $ex->getMessage());
            }
        }

        $this->updateProcessStatus($process_id, 'Success', 'stop');
        $this->logInfo($process_id, 'Processing success');
    }

    public function failed(Exception $exception): void
    {
        DB::table('work_account_process')->where('process_id', $this->process_id)
            ->update([
                'is_failed' => 1,
            ]);
        Log::channel('database')->error($exception->getMessage(), [$this->process_id]);
    }

    private function updateProcessStatus(string $process_id, string $status, string $action = null): void
    {
        $updateData = [
            'status' => $status,
            'date_update' => new DateTime('now'),
        ];

        if ($action) {
            $updateData['action'] = $action;
            $updateData['is_failed'] = ($status === 'Success') ? 0 : 1;
        }

        DB::table('work_account_process')
            ->where('process_id', $process_id)
            ->update($updateData);

        Log::channel('database')->info('Update Status ' . $status, [$process_id]);
    }

    private function getProcessData(string $process_id)
    {
        return DB::table('work_account_process')
            ->where('process_id', $process_id)
            ->first();
    }

    private function isProcessStopped(string $process_id): bool
    {
        return $this->getProcessData($process_id)->action === 'stop';
    }

    private function accountExists(string $account): bool
    {
        return DB::table('work_account_detail')
            ->where('account_number', $account)
            ->exists();
    }

    private function updateCurrentIndex(string $process_id, int $index): void
    {
        DB::table('work_account_process')
            ->where('process_id', $process_id)
            ->update(['current_index' => $index]);
    }

    private function processAccount(string $process_id, string $account, $dataProcess): bool
    {
        DB::table('work_account_detail')->where('account_number', $account)->delete();

        $header = $this->getHeader($dataProcess->header_id);
        if (empty($header)) {
            $this->logError($process_id, 'Header is empty');
            return false;
        }

        $response = $this->fetchAccountData($account, $header->headers);
        $this->storeAccountData($response, $process_id, $account);
        return true;
    }

    private function getHeader($headerId)
    {
        return DB::table('header_ups')
            ->where('headers_id', $headerId)
            ->first();
    }

    private function fetchAccountData(string $account, string $headerText)
    {
        $headers = $this->parseHeaders($headerText);
        return Http::retry(3, 500)
            ->withHeaders($headers)
            ->post('https://webapis.ups.com/doapp/api/Registration/ReadExistingAccount', $account);
    }

    private function parseHeaders(string $headerText): array
    {
        $headers = [];
        foreach (explode("\n", $headerText) as $line) {
            if (str_contains($line, ':')) {
                list($key, $value) = explode(':', $line, 2);
                $headers[trim($key)] = trim($value);
            }
        }
        return $headers;
    }

    private function storeAccountData($response, string $process_id, string $account): void
    {
        if ($response->ok()) {
            $dataBody = json_decode($response->body());
            $data = [
                'account_number' => $account,
                'json_body' => $response->body(),
                'work_account_process_id' => $process_id,
                'status' => 'Failed',
            ];

            if (isset($dataBody->Object->postalCode)) {
                $data['status'] = 'Success';
                $data['zipcode'] = $dataBody->Object->postalCode;
                $data['email'] = $dataBody->Object->aiaEmailList[0]->email ?? '';
                $data['country'] = $dataBody->Object->aiaAccountCountryCode;
            }

            DB::table('work_account_detail')->insert($data);
            $this->logInfo($process_id, 'Successfully processed account: ' . $account);
        } else {
            $this->logError($process_id, 'Failed to process account: ' . $account);
        }
    }

    private function readFileData(string $file): array
    {
        $lines = [];
        if ($handle = fopen($file, 'r')) {
            while (($line = fgets($handle)) !== false) {
                $line = trim($line);
                if ($line !== '') {
                    $lines[] = $line;
                }
            }
            fclose($handle);
        }
        return $lines;
    }

    private function logInfo(string $process_id, string $message): void
    {
        Log::channel('database')->info($message, [$process_id]);
    }

    private function logError(string $process_id, string $message): void
    {
        Log::channel('database')->error($message, [$process_id]);
    }
}
