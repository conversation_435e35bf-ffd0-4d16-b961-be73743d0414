<?php

namespace App\Logging;

use Illuminate\Log\Logger;
use Illuminate\Support\Facades\DB;
use Monolog\Logger as MonologLogger;
use Monolog\LogRecord;

class DatabaseLogger
{
    public function __invoke(array $config): MonologLogger
    {
        $logger = new MonologLogger('database');

        $logger->pushHandler(new class extends \Monolog\Handler\AbstractProcessingHandler {
            protected function write(array|LogRecord $record): void
            {
                DB::table('logs')->insert([
                    'channel' => $record['channel'],
                    'level' => $record['level_name'],
                    'message' => $record['message'],
                    'context' => json_encode($record['context']),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        });

        return $logger;
    }
}
