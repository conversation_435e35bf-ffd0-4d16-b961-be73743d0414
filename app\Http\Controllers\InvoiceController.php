<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use DateTime;

class InvoiceController extends Controller
{
	private const STATUS_TRANSACTION_BALANCE_ADD = 1;
	private const STATUS_TRANSACTION_BALANCE_MINUS = 3;
	private const STATUS_TRANSACTION_ORDER = 2;
	private const STATUS_TRANSACTION_REFUND = 4;
	
    public function chargeMoney(Request $request): JsonResponse
    {
        $data = $request->all();
        $rules = [
            'user_id' => 'required'
        ];

        $validator = Validator::make($data, $rules, $arrMsg ?? []);

        if ($validator->fails()) {
            return response()->json([
                'status' => 404,
                'message' => $validator->errors(),
            ]);
        }

        DB::transaction(function () use ($data) {
            $date_create = new DateTime('now');

            DB::table('invoices')->insert([
                'user_id' => $data['user_id'],
                'invoice_date' => $date_create,
                'amount' => $data['amount'],
                'note' => $data['note'],
                'status' => $data['status'],
                'create_at' => $date_create,
	            'payment_type' => $data['payment_type'] ?? 1,
	            'payment_method' => $data['payment_method'],
	            'is_receivable' => $data['payment_method'] === 'paylater' ? 1 : null
            ]);

			if (isset($data['payment_type']) && $data['payment_type'] == 1) {
				DB::table('users')->where('id', '=', $data['user_id'])
					->update([
						'balance' => $data['current_amount']
				]);
				$new_date = new DateTime(('now'));
				$transaction_id = uniqid('TRANS_') . date_format($new_date, 'YMdhms');
				DB::table('transaction')->insert([
					'id' => $transaction_id,
					'type' => $data['amount'] >= 0 ? self::STATUS_TRANSACTION_BALANCE_ADD : self::STATUS_TRANSACTION_BALANCE_MINUS,
					'user_id' => $data['user_id'],
					'amount' => $data['amount'],
					'current_amount' => $data['current_amount'],
					'note' => 'Add balance',
					'create_at' => $date_create,
					'date_create' => $date_create
				]);
			}
        });

        return response()->json([
            'status' => true
        ]);
    }

    public function getInvoice(Request $request): JsonResponse
    {
        $data = $request->all();
        $query = DB::table('invoices');
        $query->join('users', 'users.id', '=', 'invoices.user_id');

        if(isset($data['dateRange'])) {
            $arrDate = explode(" ", $data['dateRange']);
            $dateFrom = $arrDate[0] ?? new DateTime('now');
            $dateTo = $arrDate[2] ?? $dateFrom;
            $query->whereBetween('invoice_date', [$dateFrom, $dateTo]);
        }

        if(isset($data['users'])) {
            $query->whereIn('user_id', explode(",", $data['users']));
        }

        if(isset($data['frontEnd'])) {
	        $query->where('invoices.user_id', '=', $request->user()->id);
        }
        if(isset($data['status'])) {
			// check is_receivable
			if($data['status'] == 3) {
				$query->whereNotNull('invoices.is_receivable');
			} else {
				$query->where('invoices.status', '=', $data['status']);
			}
        }
        
	    if(isset($data['paymentMethod']) && $data['paymentMethod'] != -1) {
		    $query->where('invoices.payment_method', '=', $data['paymentMethod']);
	    }
	    
        if(isset($data['q'])) {
            $query->where(function ($q) use ($data) {
                $q->where("invoices.id", '=', $data["q"])
                    ->orWhere("invoices.note", 'like', '%' . $data["q"] . '%');
            });
        }
        $query->select(
            'invoices.id as invoice_id',
            'user_id',
            'users.name as username',
            'invoices.amount',
            'note',
            'invoices.status',
            'invoices.create_at',
	        'payment_type',
	        'payment_method',
	        'is_receivable',
	        'date_receivable'
        );

        if (isset($data['sortby']) && $data['sortby'] == 1) {
            $query->orderBy('invoices.create_at');
        } else {
            $query->orderBy('invoices.create_at', 'desc');
        }
        $data_invoice = $query->paginate($data['itemsPerPage']);
        return response()->json([
            'invoices' => $data_invoice,
            'message' => ''
        ]);
    }

    public function deleteInvoice(Request $request): JsonResponse
    {
        $data = $request->all();
        $rules = [
            'invoice_id' => 'required'
        ];

        $validator = Validator::make($data, $rules, $arrMsg ?? []);

        if ($validator->fails()) {
            return response()->json([
                'status' => 404,
                'message' => $validator->errors(),
            ]);
        }

        DB::table('invoices')->whereIn('id', explode(",", $data['invoice_id']))->delete();

        return response()->json([
            'status' => true
        ]);
    }

    public function updateStatusInvoice(Request $request): JsonResponse
    {
        $data = $request->all();
        $rules = [
            'invoice_id' => 'required',
            'status' => 'required'
        ];
        $validator = Validator::make($data, $rules, $arrMsg ?? []);

        if ($validator->fails()) {
            return response()->json([
                'status' => 404,
                'message' => $validator->errors(),
            ]);
        }

        DB::transaction(function () use ($data) {
			// check make Receivable
			if ($data['status'] === 2) {
				DB::table('invoices')->where('id', '=', $data['invoice_id'])
					->update([
						'is_receivable' => 0,
						'date_receivable' => new DateTime('now'),
					]);
			} else {
				DB::table('invoices')->where('id', '=', $data['invoice_id'])
					->update(['status' => $data['status']]);
			}
            

            if ($data['status'] == self::STATUS_TRANSACTION_BALANCE_ADD) {
                $invoice = DB::table('invoices')->where('id', '=', $data['invoice_id'])->first();
                $user = DB::table('users')->where('id', '=', $invoice->user_id)->first();
                $current_balance = $invoice->amount + $user->balance;
                DB::table('users')
                ->where('id', '=', $invoice->user_id)
                ->update(['balance' => $current_balance]);
	
	            $date_create = new DateTime('now');
	            DB::table('transaction')->insert([
		            'id' => uniqid('TRANS_') . date_format($date_create, 'YMdhms'),
		            'type' => self::STATUS_TRANSACTION_BALANCE_ADD,
		            'user_id' => $invoice->user_id,
		            'amount' => $invoice->amount,
		            'current_amount' => $current_balance,
		            'note' => 'Add balance',
		            'create_at' => $date_create,
		            'date_create' => $date_create
	            ]);
            }
        });

        return response()->json([
            'status' => true
        ]);
    }
}