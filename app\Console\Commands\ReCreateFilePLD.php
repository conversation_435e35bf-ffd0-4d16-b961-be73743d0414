<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class ReCreateFilePLD extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:re-create-file-pld';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dataProcess = DB::table('work_pld_file_process')
            ->where('status', '=', 'Error')
            ->select("tracking")
            ->get()
            ->pluck('tracking') // Use pluck to get an array of tracking numbers
            ->toArray();

        if (count($dataProcess) > 0) {
            $dataOrder = DB::table('orders')->whereIn('tracking_id', $dataProcess)->get()->toArray();
            if (count($dataOrder) <= 0) {
                return true;
            }
            $url_api_create_pld = env('URL_CREATE_PLD_FILE');

            $sql = "select book_num, IF(used = 0,1,used) as used from assigned_book_numbers
                where (used + :max1) <= 99 ORDER BY RAND() limit 1";
            $books = DB::selectOne($sql, ['max1' => count($dataOrder)]);
            if (!$books) {
                return true;
            }
            $response = Http::post($url_api_create_pld, [
                'dataOrder' => $dataOrder,
                'book_number' => $books->book_num,
                'used' => $books->used,
            ]);
            $body = json_decode($response->body());
            if (isset($body->used)) {
                DB::table('assigned_book_numbers')->where('book_num', '=', $books->book_num)
                    ->update(['used' => $body->used - 1]);
            }
            if (isset($body->dataRs)) {
                foreach ($body->dataRs as $rs) {
                    $trackingNumber = $rs->tracking_number;
                    $dataTracking = DB::table('work_pld_file_process')
                        ->where('tracking', '=', $trackingNumber)
                        ->first();

                    $data = [
                        'path_file' => $rs->path_file,
                        'status' => 'New',
                        'create_at' => now(), // Using Laravel's helper for current time
                        'attempts' => 0,
                        'error' => NULL,
                    ];

                    if ($dataTracking) {
                        if ($dataTracking->status !== 'Success') {
                            // Update existing record if status is not 'Success'
                            DB::table('work_pld_file_process')->updateOrInsert(
                                ['tracking' => $trackingNumber],
                                $data
                            );
                        }
                    } else {
                        // Insert new record if not found
                        DB::table('work_pld_file_process')->insert(
                            array_merge(['tracking' => $trackingNumber], $data) // Merge data with tracking
                        );
                    }
                }
            }
        }

        return true;
    }
}
