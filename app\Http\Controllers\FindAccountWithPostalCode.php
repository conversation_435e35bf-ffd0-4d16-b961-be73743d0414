<?php

namespace App\Http\Controllers;

use Illuminate\Database\Connection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Throwable;

class FindAccountWithPostalCode extends Controller
{
    private Connection $connection;

    public function __construct()
    {
        $this->connection = DB::connection('mysql_crawl_data');
    }

    public function findAccount(Request $request): JsonResponse
    {
        $request->validate([
            'postal_code' => 'required|string|max:10',
        ]);

        try {

            $accounts = $this->connection->table('work_account_detail')
                ->where('zipcode', '=', $request->get('postal_code'))
                ->orderByRaw('RAND() ASC')
                ->get('account_number')
                ->toArray();

            if (count($accounts) == 0) {
                $url_api = env('URL_GET_PGEOCODE');
                $response = Http::post($url_api, [
                    'postalCode' => $request->get('postal_code'),
                ]);
                $body = json_decode($response->body());

                if (isset($body)) {
                    $sql = "SELECT DISTINCT account_number,
                        (6371 * acos(
                            cos(RADIANS(:lat)) * cos(RADIANS(lat)) *
                            cos(RADIANS(`long`) - RADIANS(:long)) +
                            sin(RADIANS(:lat1)) * sin(RADIANS(lat))
                        )) AS distance
                    FROM
                        work_account_detail
                    WHERE `long` IS NOT NULL AND lat IS NOT NULL
                    ORDER BY
                        DISTANCE ASC, RAND()
                    LIMIT 50;";

                    $accounts = $this->connection->select($sql, ["lat" => $body->latitude, "lat1" => $body->latitude, "long" => $body->longitude]);
                }
            }
            if (!empty($accounts)) {
                $accountNumbersArray = array_map(function ($account) {
                    return $account->account_number;
                }, $accounts);
                $accountNumbersString = implode("\n", $accountNumbersArray);
                return response()->json(['account_numbers' => $accountNumbersString]);
            }
            return response()->json(['message' => 'Account not found', 'status' => 404], 500);
        } catch (Throwable $th) {
            return response()->json(['message' => 'Account not found', 'status' => 500], 500);
        }
    }
}
