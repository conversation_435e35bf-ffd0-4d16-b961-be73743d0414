<?php

namespace App\Http\Controllers;

use DateTime;
use Exception;
use Illuminate\Database\Connection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class UPSCheckerController extends Controller
{
    private Connection $connection;

    public function __construct()
    {
        $this->connection = DB::connection('mysql_crawl_data');
    }
    public function downloadCsv(Request $request): BinaryFileResponse|bool
    {
        $data = $request->all();
        $dataProcess =  $this->connection->table('work_account_process')->where('process_id', $data['work_account_process_id'])->first();
        if (empty($dataProcess)) {
            return false;
        }
        $path = $dataProcess->csv_path;

        return response()->download($path, 'dataAccount.csv', [
            'Content-Type' => 'text/csv',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0',
            'Pragma' => 'public',
        ]);
    }

    public function getAccount(Request $request): JsonResponse
    {
        $data = $request->all();
        return response()->json([
            'accountList' => $this->connection->table('work_account_detail')
                ->where('work_account_process_id', $data['work_account_process_id'])
                ->where('status', 'Success')
                ->paginate($query_params['itemsPerPage'] ?? 10),
        ]);
    }
    public function getHeader(Request $request): JsonResponse
    {
        $data = $request->all();
        $sql = " select is_enable,id, username, `password`, proxy
                 from `account_ups` where id = " . $data['account_id'];
        $dataRs = $this->connection->selectOne($sql);
        return response()->json([
            'status' => 200,
            'data' => $dataRs
        ]);
    }
    public function getListHeader(Request $request): JsonResponse
    {
        $dataRs = $this->connection->table('account_ups')->get();
        return response()->json([
            'status' => 200,
            'headers' => $dataRs
        ]);
    }

    public function getListCountry(): JsonResponse
    {
        $dataRs = $this->connection->table('work_account_detail')->distinct()->get(['country']);
        return response()->json([
            'status' => 200,
            'countryList' => $dataRs
        ]);
    }

    public function updateHeader(Request $request): JsonResponse
    {
        $data = $request->all();

        $account = $data['account'];
        if (isset($data['account_id'])) {
            $this->connection->table('account_ups')
                ->where('id' ,$data['account_id'])
                ->update([
                    'username' => $account['username'],
                    'password' => $account['password'],
                    'proxy' => $account['proxy'],
                    'is_enable' => $account['is_enable'],
                    'status' => 'Not Live'
                ]);
        } else {
            $this->connection->table('account_ups')->insert([
                'username' => $account['username'],
                'password' => $account['password'],
                'proxy' => $account['proxy'],
                'is_enable' => $account['is_enable'],
                'status' => 'Not Live'
            ]);
        }

        return response()->json([
            'status' => 200,
        ]);
    }

    public function getTask(Request $request): JsonResponse
    {
//        $data = $this->connection->table('work_account_process')
//            ->leftJoin('header_ups', 'work_account_process.header_id', '=', 'header_ups.headers_id')
//            ->get(['work_account_process.*', 'header_ups.header_name']);

        $sql = "SELECT
                work_account_process.process_id,
                work_account_process.file_name,
                work_account_process.`status`,
                work_account_process.date_create,
                work_account_process.date_update,
                work_account_process.header_id,
                work_account_process.action,
                total_account,
                ( SELECT COUNT( 1 ) FROM work_account_detail WHERE work_account_detail.work_account_process_id = work_account_process.process_id AND work_account_detail.`status` = 'Failed' ) AS num_account_failed,
                ( SELECT COUNT( 1 ) FROM work_account_detail WHERE work_account_detail.work_account_process_id = work_account_process.process_id AND work_account_detail.`status` = 'Success' ) AS num_account_succeed,
                ( SELECT COUNT( 1 ) FROM work_account_detail WHERE work_account_detail.work_account_process_id = work_account_process.process_id ) AS num_account_processed
            FROM
                work_account_process
                where status = 'Processing'";

        return response()->json([
            'status' => 200,
            'data' => $this->connection->select($sql),
        ]);
    }

    /**
     * @throws Exception
     */
    public function createTask(Request $request): JsonResponse
    {
        $data = $request->all();
        $token_id = uniqid();

        $file_name = '/ups_checker/' . $token_id . '/' . $data['fileName'];
        Storage::disk('public')->put($file_name, base64_decode($data['fileData']));
        $fileData = public_path('storage/' . $file_name);

        $lines = $this->readFileData($fileData);

        $this->connection->table('work_account_process')->insertGetId([
            'path_file' => $fileData,
            'file_name' => $data['fileName'],
            'web_path_file' => env('APP_URL') . '/storage/' . $file_name,
            'status' => 'New',
            'total_account' => count($lines),
            'num_account_processed' => 0,
            'num_account_succeed' => 0,
            'num_account_failed' => 0,
            'date_create' => new DateTime(('now')),
            'header_id' => $data['headers'],
            'action' => 'run',
            'current_index' => 0,
        ]);

        return response()->json([
            'status' => 200,
        ]);
    }

    public function updateTask(Request $request): JsonResponse
    {
        $data = $request->all();
        $dataUpdate = [];
        foreach ($data as $key => $value) {
            if ($key != 'process_id') {
                $dataUpdate[$key] = $value;
            }
        }
        $this->connection->table('work_account_process')->where('process_id', $data['process_id'])->update($dataUpdate);
        return response()->json([
            'status' => 200,
        ]);
    }

    private function readFileData($file): array
    {
        $lines = [];
        // Read file line-by-line
        $handle = fopen($file, 'r');
        if ($handle) {
            while (($line = fgets($handle)) !== false) {
                if (trim($line) !== '') {
                    $lines[] = trim($line);
                }
            }
            fclose($handle);
        }

        return $lines;
    }

    public function getAccountDetail(Request $request): JsonResponse
    {
        $query_params = $request->all();
        $query = $this->connection->table('work_account_detail');

        if (!empty($query_params['statusFilter']) && $query_params['statusFilter'] !== 'all') {
            $query->where('status', $query_params['statusFilter']);
        }
        if (!empty($query_params['countryFilter']) && $query_params['countryFilter'] !== 'All') {
            $query->where('country', $query_params['countryFilter']);
        }
        if (!empty($query_params['zipCodeFilter']) && $query_params['zipCodeFilter'] !== 'All') {
            $query->where('zipcode', $query_params['zipCodeFilter']);
        }

        if (!empty($query_params['accountFilter'])) {
            $query->where('account_number', $query_params['accountFilter']);
        }
        $data = $query->paginate($query_params['itemsPerPage'] ?? 10);
        return response()->json([
            'status' => true,
            'listAccount' => $data,
            'listZipCode' => $this->connection
                ->table('work_account_detail')
                ->whereNotNull('zipcode')
                ->distinct()
                ->limit(100)
                ->get(['zipcode']),
            'listCountry' => $this->connection->table('work_account_detail')->whereNotNull('country')->distinct()->get(['country'])
        ]);
    }

    public function filterZipCode(Request $request): JsonResponse
    {
        $query_params = $request->all();

        if (!empty($query_params['zipCodeFilter'])) {
            $sql = "select distinct zipcode from work_account_detail where zipcode is not null and zipcode like '%" . $query_params['zipCodeFilter'] . "%'";
        } else {
            $sql = "select distinct zipcode from work_account_detail where zipcode is not null limit 100";
        }
        $data = $this->connection->select($sql);
        return response()->json([
            'dataZip' => $data
        ]);
    }

    public function deleteAccount(Request $request): JsonResponse
    {
        $data = $request->all();
        if (isset($data["data_update"])) {
            foreach ($data["data_update"] as $item) {
                $this->connection->table("work_account_detail")
                    ->where("account_number", "=", $item)
                    ->delete();
            }
        }
        return response()->json([
            'status' => true
        ]);
    }
}
