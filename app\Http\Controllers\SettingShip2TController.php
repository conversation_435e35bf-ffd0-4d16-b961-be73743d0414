<?php

namespace App\Http\Controllers;

use Illuminate\Database\Connection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SettingShip2TController extends Controller
{
    private Connection $connection;

    public function __construct()
    {
        $this->connection = DB::connection('mysql_shipease');
    }

    public function getListSetting(Request $request): JsonResponse
    {
        // get list account no
        $account = $this->connection->table('account_number_ship2t')->orderBy('order_no')->get();

        $account_str = "";

        $lengthAccount = count($account);
        foreach ($account as $key => $item) {
            $account_str .= $item->account_no;
            if ($key != $lengthAccount - 1) {
                $account_str .= PHP_EOL;
            }
        }

        $settings_ship2t = $this->connection->table('settings_ship2t')->where('is_display', '=', 1)->orderBy('order_no')->get();

        return response()->json([
            'settings_ship2t' => $settings_ship2t,
            'data' => $account_str,
        ]);
    }

    public function updateSettings(Request $request): JsonResponse
    {
        $data = $request->all();
        DB::transaction(function () use ($data) {
            $clientId = "";
            $clientSecret = "";

            foreach ($data['setting'] as $item) {
                $this->connection->table('settings_ship2t')->where('key_', '=', $item['key_'])->update([
                    'value_' => $item['value_'],
                ]);
                if ($item['key_'] == 'client_id') {
                    $clientId = $item['value_'];
                }
                if ($item['key_'] == 'client_secret') {
                    $clientSecret = $item['value_'];
                }
            }

            $access_token = SettingController::getAccessToken($clientId, $clientSecret);
            DB::table('access_key')->where('key_', '=', 'ship2t')->update([
                'value_' => $access_token
            ]);
        });
        return response()->json([
            'status' => true
        ]);
    }

    public function updateListAccountNoForShip2t(Request $request): bool
    {
        $data = $request->all();

        $account_str = str_replace("\r\n", "\n", $data['account_str']);
        $arr_account = explode("\n", $account_str);

        DB::connection('mysql_shipease')->transaction(function () use ($arr_account) {
            DB::connection('mysql_shipease')->table('account_number_ship2t')->delete();

            foreach ($arr_account as $key => $item) {
                DB::connection('mysql_shipease')->table('account_number_ship2t')->insert([
                    'account_no' => str_replace(' ', '', $item),
                    'order_no' => $key
                ]);
            }
        });

        if ($data['isRestartServer']) {
            DB::connection('mysql_shipease')->table('settings_ship2t')->where('key_', '=', 'index_account')->update([
                'value_' => 0
            ]);
        }
        return true;
    }
}
