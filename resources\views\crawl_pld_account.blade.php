<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Lookup</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .spinner-border {
            display: none; /* Ẩn spinner mặc định */
        }

        .btn-loading .spinner-border {
            display: inline-block; /* Hiện spinner khi loading */
        }
    </style>
</head>
<body class="bg-gray-100">
<div class="container mx-auto mt-10">
    <h2 class="text-2xl font-bold mb-5">Crawl Account PLD</h2>
    <form id="accountForm" method="POST" action="{{ route('account_pld.search') }}"
          class="bg-white p-6 rounded-lg shadow-md">
        @csrf
        <div class="mb-4">
            <label for="postal_code" class="block text-sm font-medium text-gray-700">Enter account:</label>
            <textarea class="mt-1 block w-full border border-gray-300 rounded-md p-2" id="account_list" name="account_list" rows="17"></textarea>
        </div>
        <button type="submit"
                class="btn btn-primary bg-blue-500 text-white font-semibold py-2 px-4 rounded flex items-center justify-center relative"
                id="findButton">
            Find
            <div class="spinner-border animate-spin border-2 border-white border-t-transparent rounded-full w-4 h-4 ml-2"></div>
        </button>
    </form>
    <div class="mt-3">
        <label for="account_number" class="block text-sm font-medium text-gray-700">Result:</label>
        <textarea class="mt-1 block w-full border border-gray-300 rounded-md p-2" id="result" rows="7"
                  readonly></textarea>
    </div>
    <div id="error_message" class="text-red-600 mt-2"></div>
</div>

<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script>
    $('#accountForm').on('submit', function (event) {
        event.preventDefault();
        $('#findButton').addClass('btn-loading'); // Thay đổi trạng thái nút

        $.ajax({
            type: 'POST',
            url: $(this).attr('action'),
            data: $(this).serialize(),
            success: function (response) {
                $('#result').val(response.result);
                $('#error_message').text('');
            },
            error: function (xhr) {
                $('#result').val('');
                if (xhr.responseJSON.status === 404) {
                    $('#error_message').text(xhr.responseJSON.message);
                } else {
                    $('#error_message').text('An error occurred. Please try again.');
                }
            },
            complete: function () {
                $('#findButton').removeClass('btn-loading'); // Phục hồi trạng thái nút
            }
        });
    });
</script>
</body>
</html>
