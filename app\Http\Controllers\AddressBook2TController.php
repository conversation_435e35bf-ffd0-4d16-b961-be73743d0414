<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AddressBook2TController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $data = DB::connection('mysql_shipease')->table('address_book_ship2t')->get();
        return response()->json($data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): bool
    {
        $data = $request->all();
        DB::connection('mysql_shipease')->table('address_book_ship2t')->insert($data);
        return true;
        // DB::table()
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $data = DB::connection('mysql_shipease')->table('address_book_ship2t')->where("id", "=", $id)->first();
        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): bool
    {
        $data = $request->all();
        DB::connection('mysql_shipease')->table('address_book_ship2t')->where("id", "=", $id)->update($data);
        return true;
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): bool
    {
        DB::connection('mysql_shipease')->table('address_book_ship2t')->where("id", "=", $id)->delete();
        return true;
    }
}
