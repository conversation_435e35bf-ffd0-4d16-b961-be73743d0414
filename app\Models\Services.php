<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Services extends Model
{
    use HasFactory;

    protected $table = 'services';
    protected $fillable = ['carrier_id', 'service_name', 'transit_day', 'status', 'is_deleted'];

    public function carrier(): BelongsTo
    {
        return $this->belongsTo(Carrier::class, 'carrier_id');
    }

    public function prices(): HasMany
    {
        return $this->hasMany(CustomPrice::class, 'service_id');
    }

    public function userServices(): HasMany
    {
        return $this->hasMany(UserService::class, 'service_id');
    }
}
